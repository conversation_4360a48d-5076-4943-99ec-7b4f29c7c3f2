DEP.md
Documento de Especificación de Producto Definitivo
Salonier - The Modern Hair Color Intelligence Platform
Versión: 7.0 Final
Fecha: 24 de Junio de 2025
Estado: Definitivo
Clasificación: Confidencial

Tabla de Contenidos

Visión Ejecutiva
Estrategia de Producto
Diseño de Experiencia
Arquitectura de Información
Especificaciones Funcionales
Diseño Visual & Interfaz
Arquitectura Técnica
Modelo de Datos
Integraciones & APIs
Seguridad & Compliance
Analytics & Métricas
Roadmap de Implementación
Casos de Uso Detallados
Apéndices


1. Visión Ejecutiva
1.1 Declaración de Visión

"Salonier será la plataforma que transforme cada colorista en un artista de precisión, convirtiendo la coloración capilar en una ciencia exacta y rentable a través de inteligencia artificial y diseño excepcional."

1.2 Propuesta de Valor Única
Para coloristas profesionales que luchan con formulaciones inconsistentes,
Salonier es la plataforma de gestión de color inteligente
que garantiza resultados perfectos en cada servicio
a través de IA avanzada y diseño intuitivo,
a diferencia de las soluciones genéricas de salón,
nosotros nos especializamos exclusivamente en la excelencia del color.
1.3 Principios de Producto

Precisión Primero: Cada decisión optimiza la exactitud del color
Velocidad Sin Fricciones: Máximo 3 toques para cualquier acción crítica
Transparencia Total: Costos, márgenes y procesos siempre visibles
Aprendizaje Continuo: La IA mejora con cada uso
Belleza en la Simplicidad: Interfaz minimalista estilo fintech moderna

1.4 Métricas de Éxito Clave
MétricaObjetivo Año 1Objetivo Año 3Usuarios Activos Mensuales10,000150,000Precisión de Formulación95%99%NPS (Net Promoter Score)60+75+Reducción de Tiempo por Servicio40%60%Incremento de Rentabilidad35%50%

2. Estrategia de Producto
2.1 Posicionamiento de Mercado
┌─────────────────────────────────────────┐
│           MATRIZ DE POSICIONAMIENTO      │
├─────────────────────────────────────────┤
│    Precio                               │
│     Alto  ┌─────────────┬──────────────│
│           │  Enterprise │   Salonier    │
│           │  Solutions  │   Premium     │
│           ├─────────────┼──────────────│
│           │  Generic    │   Salonier    │
│     Bajo  │  Salon Apps │   Freemium   │
│           └─────────────┴──────────────│
│           Básico    Avanzado           │
│                 Funcionalidad           │
└─────────────────────────────────────────┘
2.2 Modelo de Negocio
Estructura de Precios
PlanPrecioCaracterísticasTargetStarterGratis10 clientes, funciones básicasNuevos coloristasProfessional$49/mesIlimitado, IA completaColoristas activosStudio$149/mesMulti-estilista, analyticsSalones pequeñosEnterpriseCustomAPI, soporte dedicadoCadenas de salones
Fuentes de Ingreso Adicionales

Salonier Academy: $19/mes educación premium
Brand Partnerships: Comisiones por productos recomendados
API Access: $0.10 por llamada para integraciones

2.3 Estrategia Go-to-Market
Fase 1: Product-Led Growth (Meses 1-6)

Freemium agresivo con onboarding excepcional
Influencer partnerships con top coloristas
Contenido educativo viral en TikTok/Instagram

Fase 2: Expansión (Meses 7-12)

Programa de referidos con incentivos
Partnerships con escuelas de belleza
Presencia en ferias profesionales

Fase 3: Dominación (Año 2+)

Adquisición de competidores
Expansión internacional
Plataforma de marketplace


3. Diseño de Experiencia
3.1 Filosofía de Diseño
Inspirados en las mejores apps fintech como Revolut, N26 y Wise, aplicamos estos principios al beauty tech:
Principios Core

Claridad Radical

Sin menús complejos
Información jerárquica clara
Acciones obvias


Microinteracciones Delightful

Animaciones sutiles con propósito
Feedback háptico significativo
Celebración de logros


Personalización Inteligente

UI que se adapta al uso
Sugerencias contextuales
Modo oscuro/claro automático


Zero Learning Curve

Onboarding progresivo
Tooltips contextuales
Imposible perderse



3.2 User Journeys Optimizados
Journey 1: Primera Coloración (New User)
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  Welcome    │───▶│  3-Step     │───▶│   First     │
│  Screen     │    │  Setup      │    │   Success   │
│  (30s)      │    │  (2 min)    │    │   (5 min)   │
└─────────────┘    └─────────────┘    └─────────────┘
      │                   │                   │
      ▼                   ▼                   ▼
 "Hola Maria 👋"    "Tus marcas?"     "¡Primera fórmula
                    "Tu salón?"         perfecta! 🎉"
                    "Especialidad?"
Journey 2: Cliente Recurrente (Power User)
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Home      │───▶│   Smart     │───▶│   Apply     │
│   Swipe     │    │   Formula   │    │   Timer     │
│   (3s)      │    │   (20s)     │    │   (Live)    │
└─────────────┘    └─────────────┘    └─────────────┘
      │                   │                   │
      ▼                   ▼                   ▼
 "Laura está aquí"  "Misma fórmula   "Iniciando en
  [Swipe to start]   +5% cobertura?"   raíz..."
3.3 Principios de Interacción
Gestos Naturales

Swipe Right: Siguiente paso/Confirmar
Swipe Left: Retroceder/Cancelar
Swipe Down: Refrescar datos
Long Press: Opciones adicionales
Pinch: Zoom en fotos
Shake: Reportar problema

Feedback Multi-Sensorial

Visual: Animaciones fluidas 60fps
Háptico:

Tap suave: Confirmación
Tap medio: Alerta
Tap fuerte: Error


Sonoro: Sonidos sutiles opcionales


4. Arquitectura de Información
4.1 Estructura de Navegación
┌─────────────────────────────────────────┐
│              Tab Bar (Fijo)             │
├────────┬────────┬────────┬─────────────┤
│  Home  │ Clients│ Formula│  Business   │
│   🏠   │   👥   │   🧪   │     📊      │
└────────┴────────┴────────┴─────────────┘

Home ─┬─ Dashboard
      ├─ Quick Actions
      ├─ Today's Schedule  
      └─ Notifications

Clients ─┬─ Search/List
         ├─ Client Profile
         ├─ History
         └─ Gallery

Formula ─┬─ New Service
         ├─ Corrections
         ├─ Saved Formulas
         └─ Brand Converter

Business ─┬─ Analytics
          ├─ Inventory
          ├─ Pricing
          └─ Reports
4.2 Flujo de Información
Jerarquía de Datos

Nivel 1: Acciones críticas (Nueva coloración)
Nivel 2: Información contextual (Cliente actual)
Nivel 3: Datos históricos (Servicios anteriores)
Nivel 4: Configuración y ajustes

Principio de Revelación Progresiva
Básico → Muestra solo lo esencial
  ↓
Intermedio → Revela opciones al interactuar
  ↓
Avanzado → Acceso completo bajo demanda

5. Especificaciones Funcionales
5.1 Funcionalidades Core
5.1.1 Sistema de Captura Inteligente
Especificación: Captura multi-zona con validación en tiempo real
typescriptinterface SmartCapture {
  zones: ['roots', 'mids', 'ends', 'problem_areas'];
  validation: {
    lighting: AILightingCheck;
    focus: AutoFocusValidation;
    angle: OptimalAngleGuide;
  };
  output: {
    images: ProcessedImage[];
    metadata: CaptureMetadata;
    quality_score: number; // 0-100
  };
}
Flujo de Usuario:

Cámara se abre con guía de superposición
IA valida calidad en tiempo real
Auto-captura cuando condiciones son óptimas
Confirmación instantánea con preview

5.1.2 Motor de Formulación Multi-Zona
Especificación: Formulación diferenciada por zona capilar
typescriptinterface ZoneFormulation {
  zone: {
    name: string;
    baseLevel: number;
    targetLevel: number;
    undertone: Color;
    porosity: 'low' | 'medium' | 'high';
    previousChemicals: Chemical[];
  };
  formula: {
    products: Product[];
    ratio: number[];
    developer: Developer;
    processingTime: number;
    specialInstructions: string[];
  };
  cost: {
    products: number;
    margin: number;
    suggestedPrice: PriceRange;
  };
}
Características Únicas:

Hasta 5 zonas simultáneas
Ajuste automático por historial
Predicción de interacciones químicas
Optimización de inventario en tiempo real

5.1.3 Conversión Universal de Marcas
Especificación: Base de datos IA de equivalencias
typescriptinterface BrandConverter {
  input: {
    brand: string;
    line: string;
    shade: string;
    level: number;
    tone: string;
  };
  output: {
    exact_matches: Match[];
    close_matches: Match[];
    confidence: number;
    adjustments: string[];
  };
}
Cobertura:

30+ marcas profesionales
10,000+ referencias cruzadas
Actualización mensual vía IA
Feedback loop de usuarios

5.1.4 Análisis de Rentabilidad en Tiempo Real
Especificación: Dashboard financiero instantáneo
typescriptinterface ProfitabilityEngine {
  service: {
    basePrice: number;
    productCost: number;
    timeInvested: number;
    overhead: number;
  };
  analytics: {
    grossMargin: number;
    netMargin: number;
    hourlyRate: number;
    comparisonToAverage: number;
  };
  optimization: {
    suggestedPrice: number;
    upsellOpportunities: Upsell[];
    efficiencyTips: string[];
  };
}
5.2 Funcionalidades Avanzadas
5.2.1 Asistente de Voz Contextual
Activación: "Hey Salonier" o botón flotante
Comandos Soportados:

"Añade 5 minutos más a la raíz"
"Nota: cliente sensible en nuca"
"¿Cuánto tiempo queda?"
"Muestra la fórmula de nuevo"
"Calcula costo con 10% descuento"

5.2.2 Modo Corrección Express
Activación: Botón pánico en home
Flujo Acelerado:

Foto del problema (10s)
IA analiza y sugiere (20s)
Plan paso a paso (30s)
Ejecución con timer

5.2.3 Cliente VIP Intelligence
Datos Trackados:

Frecuencia de visitas
Gasto promedio
Preferencias de servicio
Sensibilidades conocidas
Predicción de necesidades

Outputs:

Recordatorios automáticos
Ofertas personalizadas
Alertas de retención
Upsell inteligente


6. Diseño Visual & Interfaz
6.1 Sistema de Diseño "Salonier Beauty"
Principios Visuales

Minimalismo Elegante

Espacios amplios tipo Apple
Tipografía bold statement
Iconografía linear delicada


Paleta Beauty-Tech
Primary Colors:
├─ Deep Purple:    #6B46C1 (Marca)
├─ Rose Gold:      #E8B4B8 (Acentos)
└─ Pearl White:    #FAFAF9 (Fondos)

Neutral Palette:
├─ Charcoal:       #1A1A1A (Textos)
├─ Warm Grey:      #64646A (Secundario)
└─ Light Grey:     #F5F5F5 (Backgrounds)

Semantic Colors:
├─ Success Green:  #10B981
├─ Warning Amber:  #F59E0B
└─ Error Red:      #EF4444

Componentes Característicos

Cards con sombras sutiles
Botones con micro-animaciones
Gradientes suaves en CTAs
Glassmorphism en modales



6.2 Tipografía
Headlines:     SF Pro Display Bold
Body:          Inter Regular/Medium
Numbers:       SF Mono (para precios/timers)
Accent:        Playfair Display (elegancia)
6.3 Componentes UI
Botón Primario
css.primary-button {
  background: linear-gradient(135deg, #6B46C1 0%, #E8B4B8 100%);
  border-radius: 16px;
  padding: 16px 32px;
  font-weight: 600;
  box-shadow: 0 4px 24px rgba(107, 70, 193, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.primary-button:active {
  transform: scale(0.98);
  box-shadow: 0 2px 12px rgba(107, 70, 193, 0.35);
}
Card de Cliente
jsx<ClientCard>
  <Avatar size="large" src={client.photo} />
  <ClientInfo>
    <Name>{client.name}</Name>
    <LastVisit>{timeAgo(client.lastVisit)}</LastVisit>
  </ClientInfo>
  <QuickActions>
    <IconButton icon="formula" />
    <IconButton icon="history" />
  </QuickActions>
</ClientCard>
Timer Inteligente
jsx<SmartTimer>
  <CircularProgress value={progress} />
  <TimeRemaining>{formatTime(remaining)}</TimeRemaining>
  <Zone>{currentZone}</Zone>
  <MicroAnimation type="pulse" />
</SmartTimer>
6.4 Animaciones & Transiciones
Principios de Movimiento

Duración: 200-400ms para la mayoría
Easing: cubic-bezier(0.4, 0, 0.2, 1)
Propósito: Cada animación guía la atención

Animaciones Signature

Formula Reveal
Fade in → Scale up → Slight bounce
Duration: 600ms total

Success Celebration
Confetti burst → Haptic feedback → Number count up
Duration: 1200ms total

Card Flip
3D rotation → Content swap → Settle
Duration: 400ms total


6.5 Modo Oscuro
Adaptación automática según:

Hora del día
Preferencias del sistema
Ambiente del salón (sensor de luz)

css[data-theme="dark"] {
  --bg-primary: #0A0A0A;
  --bg-secondary: #1A1A1A;
  --text-primary: #FAFAF9;
  --text-secondary: #A0A0A0;
  /* Colores vibrantes se mantienen para contraste */
}

7. Arquitectura Técnica
7.1 Arquitectura del Sistema
┌─────────────────────────────────────────────────────┐
│                   Frontend Layer                     │
├─────────────────┬─────────────────┬─────────────────┤
│   iOS Native    │  Android Native │   Web App       │
│  (React Native) │ (React Native)  │   (Next.js)     │
│   + Native      │  + Native       │   + PWA         │
│   Modules       │   Modules       │   Support       │
└────────┬────────┴────────┬────────┴────────┬────────┘
         │                 │                 │
         └─────────────────┼─────────────────┘
                           │
                    ┌──────▼──────┐
                    │  API Gateway │
                    │ (Kong/AWS)  │
                    └──────┬──────┘
                           │
    ┌──────────────────────┼──────────────────────┐
    │                      │                      │
┌───▼────────┐    ┌────────▼────────┐    ┌───────▼───────┐
│   Auth      │    │  Core Services  │    │  AI Services  │
│  Service    │    │   (Node.js)     │    │   (Python)    │
│(Supabase)   │    │                 │    │               │
└─────────────┘    └─────────────────┘    └───────────────┘
                           │
    ┌──────────────────────┼──────────────────────┐
    │                      │                      │
┌───▼────────┐    ┌────────▼────────┐    ┌───────▼───────┐
│ PostgreSQL │    │  Redis Cache    │    │ Object Store  │
│   (RDS)    │    │  (ElastiCache)  │    │    (S3)       │
└────────────┘    └─────────────────┘    └───────────────┘
7.2 Stack Tecnológico Detallado
Frontend
yamlMobile:
  Framework: React Native 0.74+
  Navigation: React Navigation v6
  State: Zustand + React Query
  UI: Custom Design System + Reanimated 3
  
  Native Modules:
    - Camera: react-native-vision-camera
    - Haptics: react-native-haptic-feedback  
    - Biometrics: react-native-biometrics
    - Voice: @react-native-voice/voice
    
Web:
  Framework: Next.js 14 (App Router)
  Styling: Tailwind CSS + CSS Modules
  State: Zustand + React Query
  Animations: Framer Motion
Backend
yamlAPI Layer:
  Language: TypeScript
  Framework: Fastify (performance)
  Validation: Zod
  Documentation: OpenAPI 3.0
  
Services:
  - Auth: Supabase Auth + Custom JWT
  - Formula: Core business logic
  - Analytics: Event processing
  - Notifications: Push + Email + SMS
  
Infrastructure:
  Hosting: AWS ECS Fargate
  CDN: CloudFront
  Queue: SQS for async tasks
  Monitoring: DataDog
AI/ML Pipeline
yamlModels:
  Vision: GPT-4 Vision for analysis
  NLP: GPT-4 for formulation
  Custom: TensorFlow Lite on-device
  
Pipeline:
  1. Image preprocessing (Sharp)
  2. Feature extraction (on-device)
  3. API call with optimized prompt
  4. Response caching (Redis)
  5. Continuous learning feedback
7.3 Optimizaciones de Rendimiento
Mobile Performance

Bundle Size: <10MB initial download
Startup Time: <2s cold start
Frame Rate: Consistent 60fps
Memory: <150MB average usage

Estrategias Implementadas

Code Splitting: Lazy load por feature
Image Optimization: WebP + progressive loading
Caching Strategy:

API responses: 5 min
Images: 30 days
Formulas: Indefinite


Offline First: Core features sin conexión

7.4 Escalabilidad
Capacidad Target

Concurrent Users: 100,000
Requests/sec: 10,000
Storage: 10TB growing 1TB/month
Availability: 99.95% SLA

Estrategias de Scaling
yamlHorizontal:
  - Auto-scaling groups (2-100 instances)
  - Multi-region deployment
  - Read replicas for database
  
Vertical:
  - GPU instances for AI workloads
  - High-memory cache nodes
  - Optimized database instances
  
Optimization:
  - Query optimization ongoing
  - Index management automated
  - CDN for static assets

8. Modelo de Datos
8.1 Esquema Principal
sql-- Core Entities
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type ENUM('individual', 'salon', 'chain'),
    subscription_tier ENUM('free', 'pro', 'studio', 'enterprise'),
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id),
    email VARCHAR(255) UNIQUE NOT NULL,
    role ENUM('owner', 'admin', 'colorist', 'assistant'),
    profile JSONB DEFAULT '{}',
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id),
    created_by UUID REFERENCES users(id),
    name VARCHAR(255) NOT NULL,
    contact JSONB DEFAULT '{}',
    allergies JSONB DEFAULT '[]',
    preferences JSONB DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    vip_status BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Indices for performance
    INDEX idx_clients_org (organization_id),
    INDEX idx_clients_name (name gin_trgm_ops),
    INDEX idx_clients_tags (tags)
);

CREATE TABLE hair_analyses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id),
    performed_by UUID REFERENCES users(id),
    
    -- Multi-zone analysis
    zones JSONB NOT NULL, -- {roots: {...}, mids: {...}, ends: {...}}
    
    -- AI results
    ai_analysis JSONB NOT NULL,
    confidence_score FLOAT CHECK (confidence_score >= 0 AND confidence_score <= 1),
    
    -- Images
    images JSONB DEFAULT '[]',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    INDEX idx_analyses_client (client_id),
    INDEX idx_analyses_date (created_at DESC)
);

CREATE TABLE formulas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    analysis_id UUID REFERENCES hair_analyses(id),
    created_by UUID REFERENCES users(id),
    
    -- Zone-based formulas
    zones JSONB NOT NULL, -- {roots: {products: [], time: 45}, ...}
    
    -- Costing
    total_product_cost DECIMAL(10,2),
    suggested_price DECIMAL(10,2),
    actual_price DECIMAL(10,2),
    
    -- Metadata
    notes TEXT,
    tags TEXT[] DEFAULT '{}',
    is_correction BOOLEAN DEFAULT FALSE,
    success_rating INTEGER CHECK (success_rating >= 1 AND success_rating <= 5),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    INDEX idx_formulas_tags (tags),
    INDEX idx_formulas_success (success_rating)
);

CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id UUID REFERENCES clients(id),
    formula_id UUID REFERENCES formulas(id),
    performed_by UUID REFERENCES users(id),
    
    -- Timing
    scheduled_at TIMESTAMPTZ,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    
    -- Documentation
    process_notes JSONB DEFAULT '[]', -- [{time: '14:30', note: 'Started roots', type: 'voice'}]
    photos JSONB DEFAULT '[]',
    
    -- Business
    total_charged DECIMAL(10,2),
    payment_method VARCHAR(50),
    
    -- Feedback
    client_rating INTEGER CHECK (client_rating >= 1 AND client_rating <= 5),
    client_feedback TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    INDEX idx_services_date (scheduled_at),
    INDEX idx_services_client (client_id)
);

-- Inventory Management
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id),
    
    -- Product info
    brand VARCHAR(255) NOT NULL,
    line VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(100),
    type ENUM('color', 'developer', 'treatment', 'other'),
    
    -- Inventory
    current_stock DECIMAL(10,2) DEFAULT 0,
    unit_cost DECIMAL(10,2),
    reorder_point DECIMAL(10,2),
    
    -- Metadata
    properties JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(organization_id, brand, line, code),
    INDEX idx_products_type (type),
    INDEX idx_products_stock (current_stock)
);

-- Analytics Events
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    event_type VARCHAR(100) NOT NULL,
    properties JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Partitioned by month for performance
    INDEX idx_events_user_date (user_id, created_at DESC),
    INDEX idx_events_type_date (event_type, created_at DESC)
) PARTITION BY RANGE (created_at);
8.2 Vistas Materializadas para Performance
sql-- Vista de rentabilidad por servicio
CREATE MATERIALIZED VIEW service_profitability AS
SELECT 
    s.id,
    s.client_id,
    c.name as client_name,
    s.performed_by,
    u.name as stylist_name,
    s.scheduled_at::date as service_date,
    s.total_charged,
    f.total_product_cost,
    (s.total_charged - f.total_product_cost) as gross_profit,
    CASE 
        WHEN s.total_charged > 0 
        THEN ((s.total_charged - f.total_product_cost) / s.total_charged * 100)
        ELSE 0 
    END as margin_percentage
FROM services s
JOIN clients c ON s.client_id = c.id
JOIN formulas f ON s.formula_id = f.id
JOIN users u ON s.performed_by = u.id
WHERE s.completed_at IS NOT NULL;

-- Refresh cada hora
CREATE INDEX idx_profitability_date ON service_profitability(service_date DESC);
CREATE INDEX idx_profitability_stylist ON service_profitability(performed_by);
8.3 Seguridad de Datos
Row Level Security (RLS)
sql-- Clientes solo visibles para su organización
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;

CREATE POLICY clients_organization_policy ON clients
    FOR ALL
    USING (organization_id = current_setting('app.current_organization')::uuid);

-- Formulas solo editables por su creador
CREATE POLICY formulas_edit_policy ON formulas
    FOR UPDATE
    USING (created_by = current_setting('app.current_user')::uuid);
Encriptación

At Rest: AES-256 para toda la base de datos
In Transit: TLS 1.3 mínimo
Sensitive Fields: Encriptación adicional campo por campo
PII Handling: Cumplimiento GDPR/CCPA


9. Integraciones & APIs
9.1 API REST Specification
Autenticación
httpPOST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123!"
}

Response:
{
  "access_token": "eyJhbGc...",
  "refresh_token": "eyJhbGc...",
  "user": {
    "id": "uuid",
    "role": "colorist",
    "organization_id": "uuid"
  }
}
Core Endpoints
Análisis de Color
httpPOST /api/v1/analyses
Authorization: Bearer {token}
Content-Type: multipart/form-data

{
  "client_id": "uuid",
  "images": [file1, file2, file3],
  "zones": ["roots", "mids", "ends"]
}

Response:
{
  "id": "uuid",
  "zones": {
    "roots": {
      "level": 5,
      "tone": "warm",
      "gray_percentage": 40,
      "condition": "healthy"
    },
    ...
  },
  "confidence": 0.95,
  "processing_time": 2.3
}
Generación de Fórmula
httpPOST /api/v1/formulas/generate
Authorization: Bearer {token}

{
  "analysis_id": "uuid",
  "target_color": {
    "level": 7,
    "tone": "ash"
  },
  "preferred_brands": ["wella", "loreal"],
  "constraints": {
    "avoid_ppd": true,
    "processing_time_max": 60
  }
}

Response:
{
  "id": "uuid",
  "zones": {
    "roots": {
      "products": [
        {
          "brand": "wella",
          "line": "koleston",
          "shade": "7/1",
          "amount": 45
        }
      ],
      "developer": "6%",
      "time": 45,
      "cost": 12.50
    },
    ...
  },
  "total_cost": 27.50,
  "suggested_price": 125.00,
  "warnings": [],
  "alternatives": []
}
9.2 GraphQL API (Futuro)
graphqltype Query {
  # Cliente con historial completo
  client(id: ID!): Client
  
  # Búsqueda inteligente
  searchClients(
    query: String!
    filters: ClientFilters
    limit: Int = 20
  ): ClientConnection
  
  # Analytics
  businessMetrics(
    period: DateRange!
    groupBy: MetricGrouping
  ): BusinessMetrics
}

type Mutation {
  # Crear servicio completo
  createService(
    input: ServiceInput!
  ): ServicePayload
  
  # Actualizar inventario
  updateInventory(
    productId: ID!
    adjustment: Float!
    reason: String
  ): Product
}

type Subscription {
  # Actualizaciones en tiempo real
  serviceUpdates(serviceId: ID!): ServiceUpdate
  
  # Notificaciones
  notifications(userId: ID!): Notification
}
9.3 Webhooks
Eventos Disponibles
yamlEvents:
  - service.completed
  - formula.created
  - client.birthday
  - inventory.low_stock
  - subscription.updated
  
Payload Example:
{
  "event": "service.completed",
  "timestamp": "2025-06-24T10:30:00Z",
  "data": {
    "service_id": "uuid",
    "client_id": "uuid",
    "total": 125.00,
    "rating": 5
  }
}
9.4 Integraciones de Terceros
Pasarelas de Pago

Stripe: Principal para todos los mercados
PayPal: Opción secundaria
Local Methods: Según región

Comunicaciones

Twilio: SMS para recordatorios
SendGrid: Emails transaccionales
OneSignal: Push notifications

Analytics & Monitoring

Mixpanel: User behavior
Sentry: Error tracking
DataDog: Infrastructure monitoring
Hotjar: Session recordings

Integraciones Futuras

QuickBooks: Contabilidad
Instagram: Portfolio sync
Google Calendar: Sincronización
Shopify: Venta de productos


10. Seguridad & Compliance
10.1 Arquitectura de Seguridad
Modelo de Seguridad en Capas
┌─────────────────────────────────────┐
│         WAF (CloudFlare)            │ Layer 7
├─────────────────────────────────────┤
│      API Gateway (Rate Limiting)    │ Layer 6
├─────────────────────────────────────┤
│     Authentication (JWT + MFA)      │ Layer 5
├─────────────────────────────────────┤
│    Authorization (RBAC + ABAC)      │ Layer 4
├─────────────────────────────────────┤
│     Application Security (OWASP)    │ Layer 3
├─────────────────────────────────────┤
│      Data Security (Encryption)     │ Layer 2
├─────────────────────────────────────┤
│    Infrastructure (VPC, SG, IAM)    │ Layer 1
└─────────────────────────────────────┘
10.2 Compliance Framework
GDPR Compliance
yamlData Subject Rights:
  - Right to Access: Export in 24h
  - Right to Rectification: In-app editing
  - Right to Erasure: Full deletion flow
  - Right to Portability: JSON/CSV export
  - Right to Object: Granular opt-outs

Technical Measures:
  - Privacy by Design
  - Data Minimization
  - Purpose Limitation
  - Pseudonymization where possible
  - Regular audits
CCPA Compliance
yamlConsumer Rights:
  - Know what data is collected
  - Delete personal information
  - Opt-out of data sale
  - Non-discrimination

Implementation:
  - Clear privacy policy
  - Opt-out mechanism
  - Data inventory maintained
  - Vendor management
Industry Specific
yamlCosmetics Regulations:
  - Ingredient tracking
  - Allergen database
  - Adverse event reporting
  - Product recall system

Professional Standards:
  - License verification
  - Insurance validation
  - Continuing education tracking
10.3 Security Controls
Access Control
typescript// Multi-factor authentication
interface MFAConfig {
  methods: ['sms', 'totp', 'biometric'];
  required_for: ['login', 'sensitive_actions'];
  remember_device: boolean;
  session_timeout: 30; // minutes
}

// Role-based permissions
enum Permissions {
  // Clients
  CLIENT_VIEW = 'client:view',
  CLIENT_EDIT = 'client:edit',
  CLIENT_DELETE = 'client:delete',
  
  // Formulas
  FORMULA_CREATE = 'formula:create',
  FORMULA_VIEW_ALL = 'formula:view:all',
  FORMULA_EDIT_OWN = 'formula:edit:own',
  
  // Business
  ANALYTICS_VIEW = 'analytics:view',
  PRICING_EDIT = 'pricing:edit',
  INVENTORY_MANAGE = 'inventory:manage'
}
Data Protection
yamlEncryption:
  At Rest:
    - Database: AES-256-GCM
    - File Storage: SSE-S3
    - Backups: Encrypted snapshots
    
  In Transit:
    - TLS 1.3 minimum
    - Certificate pinning on mobile
    - HSTS enabled
    
  Application Level:
    - Sensitive fields encrypted
    - Key rotation quarterly
    - HSM for key management
Audit & Monitoring
sql-- Audit log structure
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY,
    user_id UUID,
    action VARCHAR(100),
    resource_type VARCHAR(50),
    resource_id UUID,
    changes JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Automated alerts for:
-- - Multiple failed login attempts
-- - Unusual access patterns
-- - Data export requests
-- - Permission changes

11. Analytics & Métricas
11.1 Framework de Métricas
North Star Metric
Servicios Exitosos por Usuario Activo Mensual
Formula: Successful Services / MAU
Target: >8 servicios/usuario/mes
Métricas HEART de Google
CategoríaMétricaTargetMediciónHappinessNPS Score>60Encuesta trimestralEngagementDAU/MAU>60%Analytics diarioAdoptionFeature adoption>80%Por feature lanzadaRetention90-day retention>70%Cohorte analysisTask SuccessFormula accuracy>95%Post-servicio
11.2 Dashboards
Dashboard Ejecutivo
yamlKPIs en Tiempo Real:
  - MRR y growth rate
  - Active users (DAU/WAU/MAU)
  - Churn rate y razones
  - Formula success rate
  - Average revenue per user
  
Visualizaciones:
  - Trend lines (13 meses)
  - Cohort retention curves
  - Geographic heat maps
  - Feature adoption funnels
Dashboard Operacional
yamlMétricas de Servicio:
  - Services per day
  - Average service time
  - Product usage efficiency
  - Inventory turnover
  - Staff utilization
  
Alertas Automáticas:
  - Low inventory (<20%)
  - High correction rate (>15%)
  - Client complaints
  - System errors
11.3 Eventos de Tracking
Eventos Críticos
javascript// Onboarding
track('user_signed_up', {
  method: 'email|google|apple',
  referral_source: string,
  organization_type: string
});

track('onboarding_completed', {
  duration_seconds: number,
  skipped_steps: string[],
  selected_brands: string[]
});

// Core Usage
track('formula_created', {
  zones_count: number,
  brands_used: string[],
  ai_confidence: number,
  manual_adjustments: boolean,
  creation_time_seconds: number
});

track('service_completed', {
  formula_id: string,
  client_id: string,
  duration_minutes: number,
  products_cost: number,
  total_charged: number,
  profit_margin: number,
  satisfaction_rating: number
});

// Business Impact
track('inventory_reordered', {
  products: array,
  total_cost: number,
  automatic: boolean
});

track('price_optimized', {
  service_type: string,
  old_price: number,
  new_price: number,
  margin_change: number
});
11.4 Reportes Automatizados
Reporte Semanal (Lunes 8AM)
Subject: Tu semana en Salonier 📊

Hola [Nombre],

La semana pasada lograste:
- 23 servicios completados (+15% vs anterior)
- $3,450 en ingresos (+8%)
- 96% satisfacción de clientes
- $892 en ganancias netas

Top 3 servicios más rentables:
1. Balayage completo - 82% margen
2. Corrección de color - 78% margen
3. Retoque de raíz - 75% margen

Clientes que necesitan agendar:
- Laura M. (hace 8 semanas)
- Carmen R. (hace 7 semanas)

¡Sigue así! 💜

12. Roadmap de Implementación
12.1 Fase 1: MVP (Q1 2025)
Sprint 1-2: Foundation (Semanas 1-4)

 Setup técnico completo
 Sistema de autenticación
 Diseño del sistema UI
 Modelos de datos core

Sprint 3-4: Core Features (Semanas 5-8)

 Captura y análisis IA
 Formulación básica
 Gestión de clientes
 Flujo de servicio completo

Sprint 5-6: Polish & Launch (Semanas 9-12)

 Optimización de performance
 Beta testing (100 usuarios)
 Fixes y mejoras
 Soft launch

Entregables:

iOS App en TestFlight
Android App en Beta
500 usuarios beta
90% formula accuracy

12.2 Fase 2: Growth (Q2 2025)
Features Nuevas

 Conversión de marcas completa
 Analytics avanzado
 Modo offline
 Multi-idioma (ES, PT)
 Integraciones de pago

Objetivos:

5,000 usuarios activos
$100K MRR
4.5+ rating en stores
<3% churn mensual

12.3 Fase 3: Scale (Q3-Q4 2025)
Expansión

 Web app completa
 API pública
 Marketplace de fórmulas
 Hardware integrations
 Enterprise features

Objetivos:

20,000 usuarios
$500K MRR
3 países lanzados
Serie A preparada

12.4 Visión 2026+
Innovación

AI Avanzada: Predicción de tendencias
IoT: Smart mirrors y bowls
Blockchain: Certificación de servicios
AR/VR: Training inmersivo
Global: 10+ países


13. Casos de Uso Detallados
13.1 Caso: Primera Vez con Cliente Nuevo
Actores

María (Colorista, 5 años experiencia)
Laura (Cliente nueva, quiere cambio dramático)

Flujo Detallado
1. RECEPCIÓN (2 min)
María: Abre Salonier → Tap "Nuevo Cliente"
App: "¿Cómo se llama?" → Teclado optimizado
María: Escribe "Laura García"
App: Crea perfil → Muestra checklist de seguridad

2. SEGURIDAD (1 min)
App: "¿Alergias conocidas?"
Laura: "Tuve reacción a tinte de farmacia"
María: Marca "Sensibilidad a PPD"
App: ⚠️ "Productos sin PPD serán sugeridos"

3. DIAGNÓSTICO (2 min)
App: Cámara con guía → "RAÍZ - Alinea aquí"
María: Captura con auto-focus
App: ✅ "Calidad perfecta" → Siguiente zona
[Repite para medios y puntas]

App muestra análisis:
┌─────────────────────────┐
│ Nivel base: 4           │
│ Canas: 15%              │
│ Porosidad: Media        │
│ Decoloración previa: No │
└─────────────────────────┘

María: Confirma con tap

4. OBJETIVO (2 min)
Laura: Muestra foto de Instagram
María: Captura la referencia
App: "Analizando..." → 3 segundos

App muestra:
┌─────────────────────────┐
│ ⚠️ REALITY CHECK        │
│ Para este rubio (9):    │
│ • 2 sesiones mínimo     │
│ • $380 inversión total  │
│ • Mantenimiento mensual │
│                         │
│ [Ver alternativas]      │
└─────────────────────────┘

5. DECISIÓN (1 min)
María: Tap "Ver alternativas"
App: Muestra 3 opciones realistas
Laura: Elige balayage nivel 7
María: Confirma selección

6. FÓRMULA (30 seg)
App genera instantáneamente:
┌─────────────────────────┐
│ FÓRMULA SIN PPD         │
├─────────────────────────┤
│ RAÍZ:                   │
│ Wella 6.0 + 6% (30g+30ml)│
│ 45 minutos              │
├─────────────────────────┤
│ MEDIOS A PUNTAS:        │
│ Decoloración suave      │
│ + Olaplex               │
│ 35 minutos              │
├─────────────────────────┤
│ Costo: $31.50           │
│ Precio sugerido: $140   │
└─────────────────────────┘

7. PROCESO (Durante servicio)
App: Timers automáticos por zona
María: "Hey Salonier, nota: agregué 5ml violeta"
App: ✅ Nota guardada con timestamp

8. FINALIZACIÓN (1 min)
App: Muestra antes/después
Laura: "¡Me encanta!"
María: Tap calificación 5 estrellas
App: Genera factura → Envía por WhatsApp
App: "Próxima cita sugerida: 15 agosto"

TIEMPO TOTAL EN APP: 10 minutos
RESULTADO: Cliente feliz, documentación completa
13.2 Caso: Corrección de Emergencia
Contexto

Sofía llega llorando con cabello naranja
Intentó decoloración casera
Necesita solución urgente

Flujo Express
1. MODO PÁNICO (10 seg)
Estilista: Long press botón home
App: "¿Corrección urgente?" → SI
App: Salta todo protocolo normal

2. DIAGNÓSTICO RÁPIDO (30 seg)
App: "Foto del problema" → Captura
IA analiza: "Decoloración nivel 7 con subtono naranja fuerte"

3. PLAN DE ACCIÓN (30 seg)
App genera:
┌─────────────────────────────────┐
│ CORRECCIÓN EN 3 PASOS           │
│                                 │
│ 1. Matizar (20 min)             │
│    Toner 9.16 + 6vol            │
│                                 │
│ 2. Tratamiento (15 min)         │
│    Olaplex No.2                 │
│                                 │
│ 3. Gloss final (10 min)         │
│    Demi 8.11 + 6vol             │
│                                 │
│ Tiempo total: 45 min            │
│ Costo: $45 | Precio: $180       │
└─────────────────────────────────┘

4. EJECUCIÓN
App: Guía paso a paso con timers
Resultado: Cliente aliviada
13.3 Caso: Optimización de Inventario
Contexto

Salón con 5 coloristas
Pierden dinero por mal manejo de stock

Implementación
ANTES DE SALONIER:
- Compras por intuición
- 30% productos caducan
- Quiebres de stock frecuentes
- Sin visibilidad de costos reales

CON SALONIER:

1. SETUP INICIAL (1 hora)
Admin: Escanea todos los productos
App: Auto-completa info vía código de barras
Admin: Ingresa costos y stock actual

2. USO DIARIO
Cada servicio:
- Stock se actualiza automáticamente
- Costo real calculado al gramo
- Alertas antes de quiebre

3. OPTIMIZACIÓN MENSUAL
App genera reporte:
┌────────────────────────────────┐
│ RECOMENDACIONES DE COMPRA      │
├────────────────────────────────┤
│ URGENTE (3 días stock):        │
│ • Wella 6.0 - Pedir 10 tubos   │
│ • Revelador 20vol - 2 litros   │
│                                │
│ OPTIMIZAR (exceso stock):      │
│ • Reducir pedido de tonos rojos│
│ • 8.3 próximo a caducar - usar │
│                                │
│ AHORRO POTENCIAL: $450/mes     │
└────────────────────────────────┘

RESULTADO:
- 25% reducción en costos
- 0 quiebres de stock
- 90% menos desperdicio

14. Apéndices
Apéndice A: Glosario Técnico
TérminoDefiniciónPPDPara-fenilendiamina, alérgeno común en tintesUndertonePigmento subyacente natural del cabelloDeveloperPeróxido activador (6%, 9%, 12% vol)Virgin HairCabello sin procesos químicos previosPorosityCapacidad del cabello de absorber humedadLevelEscala 1-10 de oscuro a claroToneMatiz del color (ceniza, dorado, etc)
Apéndice B: Fórmulas de Cálculo
Cálculo de Rentabilidad
javascriptfunction calculateProfitability(service) {
  const productCost = service.products.reduce((sum, product) => {
    return sum + (product.amount * product.unitCost);
  }, 0);
  
  const laborCost = service.duration * hourlyRate;
  const overhead = (productCost + laborCost) * 0.3;
  
  const totalCost = productCost + laborCost + overhead;
  const profit = service.price - totalCost;
  const margin = (profit / service.price) * 100;
  
  return {
    productCost,
    laborCost,
    overhead,
    totalCost,
    profit,
    margin
  };
}
Predicción de Tiempo de Procesamiento
javascriptfunction predictProcessingTime(analysis, formula) {
  let baseTime = formula.baseProcessingTime;
  
  // Ajustes por factores
  const factors = {
    porosity: {
      high: 0.8,    // -20% tiempo
      medium: 1.0,  // Sin cambio
      low: 1.2      // +20% tiempo
    },
    grayPercentage: {
      factor: 1 + (analysis.grayPercentage / 200) // +0.5% por cada 1% de canas
    },
    previousChemical: {
      virgin: 1.0,
      colored: 1.1,
      bleached: 1.2
    }
  };
  
  return baseTime * 
         factors.porosity[analysis.porosity] * 
         factors.grayPercentage.factor *
         factors.previousChemical[analysis.chemicalHistory];
}
Apéndice C: Prompts de IA
Análisis de Color
You are an expert hair colorist with 20 years of experience.
Analyze this hair image and provide:

1. Natural level (1-10 scale)
2. Underlying pigment (warm/neutral/cool)
3. Gray percentage (0-100%)
4. Porosity assessment (low/medium/high)
5. Visible damage or chemical processing
6. Specific observations for formulation

Respond in JSON format with confidence scores for each assessment.
Generación de Fórmula
Given this hair analysis and target color, create a professional formula:

Current: [analysis data]
Target: [desired result]
Constraints: [allergies, time, preferences]

Provide:
1. Zone-specific formulas (roots/mids/ends)
2. Product recommendations with amounts
3. Processing times with rationale
4. Total cost calculation
5. Potential risks or considerations
6. Alternative options if available

Format as structured JSON optimized for mobile display.
Apéndice D: Métricas de Performance
Mobile App Performance Budget
yamlInitial Load:
  - First Contentful Paint: <1.2s
  - Time to Interactive: <3.5s
  - Bundle Size: <10MB
  
Runtime:
  - Frame Rate: 60fps consistent
  - Memory Usage: <150MB average
  - Battery Impact: <5% per hour
  
API Performance:
  - p50 Response Time: <200ms
  - p95 Response Time: <500ms
  - p99 Response Time: <1s
Apéndice E: Checklist de Lanzamiento
Pre-Launch (2 semanas antes)

 App Store assets preparados
 Beta testing completado (100+ usuarios)
 Documentación usuario final
 Videos tutoriales grabados
 Soporte 24/7 configurado
 Monitoring y alertas activos

Launch Day

 Publicación en App Store/Play Store
 Anuncio en redes sociales
 Email a lista de espera
 Press release a medios
 Webinar de lanzamiento
 Equipo de soporte listo

Post-Launch (Primera semana)

 Monitoring de reseñas
 Respuesta rápida a feedback
 Hotfixes si necesario
 Análisis de métricas
 Ajustes de onboarding
 Celebración con equipo 🎉


Control de Documento
VersiónFechaCambiosAprobado por7.024/06/2025Documento definitivo con todas las mejorasProduct Team
Estado: APROBADO PARA DESARROLLO
Próximos Pasos:

Revisión técnica final
Estimación de recursos
Kick-off con equipo
Sprint planning
¡Construir el futuro de la coloración! 💜


FIN DEL DOCUMENTO
"La belleza de la precisión, la magia de la tecnología"
Salonier © 2025 - Transformando la industria del color