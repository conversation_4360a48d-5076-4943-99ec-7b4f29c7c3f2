# Salonier - Plataforma Inteligente para Coloristas

Una plataforma web moderna diseñada específicamente para coloristas profesionales que combina inteligencia artificial con gestión de negocio para revolucionar la industria del color capilar.

## 🎨 Características Principales

### 🤖 Análisis IA de Color
- **Captura de fotos por zonas**: Raíz, medios y puntas
- **Análisis automático**: Nivel base, porcentaje de canas, porosidad
- **Generación de fórmulas**: Recomendaciones precisas por zona
- **Cálculo de costos**: Precio automático basado en productos utilizados

### 👥 Gestión de Clientes
- **Perfiles completos**: Información personal, preferencias y historial
- **Seguimiento de alergias**: Sistema de alertas por severidad
- **Galería de fotos**: Antes y después de cada servicio
- **Historial detallado**: Todos los servicios realizados

### 📦 Control de Inventario
- **Gestión de productos**: Colores, reveladores y tratamientos
- **Alertas de stock bajo**: Notificaciones automáticas
- **Cálculo de costos**: En tiempo real por servicio
- **Análisis de rentabilidad**: Margen de ganancia por producto

### ⏱️ Servicios y Timers
- **Timers inteligentes**: Por zona y tipo de proceso
- **Documentación automática**: Notas de voz y fotos durante el servicio
- **Seguimiento en tiempo real**: Progreso visual del proceso

### 📊 Analytics y Reportes
- **Métricas de negocio**: Ingresos, servicios, ticket promedio
- **Análisis de retención**: Seguimiento de clientes
- **Servicios populares**: Ranking por rentabilidad
- **Objetivos y metas**: Seguimiento de progreso mensual

## 🚀 Tecnologías Utilizadas

- **Frontend**: Next.js 14 con App Router
- **Lenguaje**: TypeScript
- **Estilos**: Tailwind CSS
- **Componentes**: Componentes personalizados con Radix UI
- **Iconos**: Lucide React
- **Animaciones**: CSS Animations y Framer Motion
- **Estado**: React Context API
- **Datos**: Mock data (preparado para Supabase)

## 🛠️ Instalación y Configuración

### Prerrequisitos
- Node.js 18+
- npm o yarn

### Pasos de instalación

1. **Clonar el repositorio**
```bash
git clone [repository-url]
cd salonier
```

2. **Instalar dependencias**
```bash
npm install
```

3. **Ejecutar en modo desarrollo**
```bash
npm run dev
```

4. **Abrir en el navegador**
```
http://localhost:3000
```

## 🔐 Credenciales de Demo

### Usuario Propietario
- **Email**: <EMAIL>
- **Contraseña**: demo123
- **Rol**: Propietario del salón

### Usuario Colorista
- **Email**: <EMAIL>
- **Contraseña**: demo123
- **Rol**: Colorista profesional
