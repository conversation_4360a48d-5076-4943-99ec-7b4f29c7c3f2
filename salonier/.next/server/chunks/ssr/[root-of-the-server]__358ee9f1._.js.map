{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center rounded-xl font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\"\n    \n    const variants = {\n      primary: \"bg-gradient-to-r from-primary to-secondary text-primary-foreground shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]\",\n      secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n      outline: \"border border-border bg-background hover:bg-muted hover:text-muted-foreground\",\n      ghost: \"hover:bg-muted hover:text-muted-foreground\",\n      destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\"\n    }\n    \n    const sizes = {\n      sm: \"h-9 px-3 text-sm\",\n      md: \"h-11 px-6 text-sm\",\n      lg: \"h-12 px-8 text-base\"\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { useAuth } from '@/lib/auth-context'\nimport { \n  Home, \n  Users, \n  Palette, \n  Package, \n  BarChart3, \n  Timer, \n  Settings,\n  LogOut,\n  Menu,\n  X\n} from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Clientes', href: '/clients', icon: Users },\n  { name: 'Formulación', href: '/formulas', icon: Palette },\n  { name: 'Inventario', href: '/inventory', icon: Package },\n  { name: 'Servicios', href: '/services', icon: Timer },\n  { name: 'Analytics', href: '/analytics', icon: BarChart3 },\n  { name: 'Configuración', href: '/settings', icon: Settings },\n]\n\nexport function Sidebar() {\n  const [isOpen, setIsOpen] = useState(false)\n  const pathname = usePathname()\n  const { user, logout } = useAuth()\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <div className=\"lg:hidden fixed top-4 left-4 z-50\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setIsOpen(!isOpen)}\n          className=\"bg-white/90 backdrop-blur-sm shadow-lg border-gray-200\"\n        >\n          {isOpen ? <X className=\"h-4 w-4\" /> : <Menu className=\"h-4 w-4\" />}\n        </Button>\n      </div>\n\n      {/* Overlay for mobile */}\n      {isOpen && (\n        <div \n          className=\"lg:hidden fixed inset-0 bg-black/50 z-40\"\n          onClick={() => setIsOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={cn(\n        \"fixed inset-y-0 left-0 z-50 w-64 bg-card border-r border-border transform transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:inset-0\",\n        isOpen ? \"translate-x-0\" : \"-translate-x-full\"\n      )}>\n        <div className=\"flex flex-col h-full\">\n          {/* Logo */}\n          <div className=\"flex items-center px-6 py-4 border-b border-border\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center\">\n              <span className=\"text-sm font-bold text-white\">S</span>\n            </div>\n            <span className=\"ml-3 text-xl font-bold gradient-text\">Salonier</span>\n          </div>\n\n          {/* User info */}\n          <div className=\"px-6 py-4 border-b border-border\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.name.charAt(0)}\n                </span>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-foreground truncate\">\n                  {user?.name}\n                </p>\n                <p className=\"text-xs text-muted-foreground truncate\">\n                  {user?.role === 'owner' ? 'Propietario' : \n                   user?.role === 'colorist' ? 'Colorista' : \n                   user?.role === 'admin' ? 'Administrador' : 'Asistente'}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsOpen(false)}\n                  className={cn(\n                    \"group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200\",\n                    isActive\n                      ? \"bg-primary text-primary-foreground shadow-md\"\n                      : \"text-muted-foreground hover:text-foreground hover:bg-muted\"\n                  )}\n                >\n                  <item.icon className={cn(\n                    \"mr-3 h-5 w-5 transition-colors\",\n                    isActive ? \"text-primary-foreground\" : \"text-muted-foreground group-hover:text-foreground\"\n                  )} />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Logout */}\n          <div className=\"px-4 py-4 border-t border-border\">\n            <Button\n              variant=\"ghost\"\n              className=\"w-full justify-start text-muted-foreground hover:text-foreground\"\n              onClick={logout}\n            >\n              <LogOut className=\"mr-3 h-5 w-5\" />\n              Cerrar Sesión\n            </Button>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAnBA;;;;;;;;;AAqBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,mMAAA,CAAA,OAAI;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAY,MAAM,oMAAA,CAAA,QAAK;IAAC;IAClD;QAAE,MAAM;QAAe,MAAM;QAAa,MAAM,wMAAA,CAAA,UAAO;IAAC;IACxD;QAAE,MAAM;QAAc,MAAM;QAAc,MAAM,wMAAA,CAAA,UAAO;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAa,MAAM,oMAAA,CAAA,QAAK;IAAC;IACpD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,YAAS;IAAC;IACzD;QAAE,MAAM;QAAiB,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;CAC5D;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAE/B,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,UAAU,CAAC;oBAC1B,WAAU;8BAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAAe,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;YAKzD,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;0BAK7B,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,iKACA,SAAS,kBAAkB;0BAE3B,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,MAAM,KAAK,OAAO;;;;;;;;;;;kDAGvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,MAAM;;;;;;0DAET,8OAAC;gDAAE,WAAU;0DACV,MAAM,SAAS,UAAU,gBACzB,MAAM,SAAS,aAAa,cAC5B,MAAM,SAAS,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;sCAOpD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gGACA,WACI,iDACA;;sDAGN,8OAAC,KAAK,IAAI;4CAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACrB,kCACA,WAAW,4BAA4B;;;;;;wCAExC,KAAK,IAAI;;mCAdL,KAAK,IAAI;;;;;4BAiBpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;;kDAET,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  icon?: React.ReactNode\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, icon, ...props }, ref) => {\n    return (\n      <div className=\"space-y-2\">\n        {label && (\n          <label className=\"text-sm font-medium text-foreground\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {icon && (\n            <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {icon}\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              \"flex h-11 w-full rounded-xl border border-border bg-input px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n              icon && \"pl-10\",\n              error && \"border-destructive focus-visible:ring-destructive\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n        </div>\n        {error && (\n          <p className=\"text-sm text-destructive\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAClD,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;oBACZ,sBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGL,8OAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4VACA,QAAQ,SACR,SAAS,qDACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;;;;;;;YAGZ,uBACC,8OAAC;gBAAE,WAAU;0BAA4B;;;;;;;;;;;;AAIjD;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, <PERSON>, Sun, Moon } from 'lucide-react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { useAuth } from '@/lib/auth-context'\n\ninterface HeaderProps {\n  title: string\n  subtitle?: string\n}\n\nexport function Header({ title, subtitle }: HeaderProps) {\n  const { user, organization } = useAuth()\n\n  return (\n    <header className=\"bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-30\">\n      <div className=\"px-4 sm:px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Title Section */}\n          <div className=\"flex-1 min-w-0 lg:pl-0 pl-12\">\n            <h1 className=\"text-xl sm:text-2xl font-bold text-gray-900 truncate\">{title}</h1>\n            {subtitle && (\n              <p className=\"text-sm text-gray-600 mt-1 hidden sm:block\">{subtitle}</p>\n            )}\n          </div>\n\n          {/* Actions Section */}\n          <div className=\"flex items-center space-x-2 sm:space-x-4\">\n            {/* Search */}\n            <div className=\"hidden lg:block\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <Input\n                  placeholder=\"Buscar clientes, servicios...\"\n                  className=\"pl-10 w-48 xl:w-64\"\n                />\n              </div>\n            </div>\n\n            {/* Mobile Search Button */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"lg:hidden\">\n              <Search className=\"h-5 w-5\" />\n            </Button>\n\n            {/* Notifications */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n              <Bell className=\"h-5 w-5\" />\n              <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full text-xs flex items-center justify-center text-white\">\n                3\n              </span>\n            </Button>\n\n            {/* Theme Toggle - Hidden on mobile */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"hidden sm:flex\">\n              <Sun className=\"h-5 w-5\" />\n            </Button>\n\n            {/* User Menu */}\n            <div className=\"flex items-center space-x-3 pl-2 sm:pl-4 border-l border-gray-200\">\n              <div className=\"hidden md:block text-right\">\n                <p className=\"text-sm font-medium text-gray-900\">{user?.name}</p>\n                <p className=\"text-xs text-gray-600\">{organization?.name}</p>\n              </div>\n              <div className=\"w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-400 rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.name.charAt(0)}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAe;IACrD,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAErC,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwD;;;;;;4BACrE,0BACC,8OAAC;gCAAE,WAAU;0CAA8C;;;;;;;;;;;;kCAK/D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAMhB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;0CAC1C,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAIpB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;;kDAC1C,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAA+G;;;;;;;;;;;;0CAMjI,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;0CAC1C,cAAA,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAIjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAqC,MAAM;;;;;;0DACxD,8OAAC;gDAAE,WAAU;0DAAyB,cAAc;;;;;;;;;;;;kDAEtD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,MAAM,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrC", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/loading.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12'\n  }\n\n  return (\n    <div className={cn('animate-spin', sizeClasses[size], className)}>\n      <svg\n        className=\"h-full w-full\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        fill=\"none\"\n        viewBox=\"0 0 24 24\"\n      >\n        <circle\n          className=\"opacity-25\"\n          cx=\"12\"\n          cy=\"12\"\n          r=\"10\"\n          stroke=\"currentColor\"\n          strokeWidth=\"4\"\n        />\n        <path\n          className=\"opacity-75\"\n          fill=\"currentColor\"\n          d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n        />\n      </svg>\n    </div>\n  )\n}\n\ninterface LoadingScreenProps {\n  message?: string\n}\n\nexport function LoadingScreen({ message = 'Cargando...' }: LoadingScreenProps) {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50 flex items-center justify-center\">\n      <div className=\"text-center space-y-6 animate-fade-in\">\n        <div className=\"mx-auto w-20 h-20 bg-gradient-to-r from-purple-600 to-pink-400 rounded-2xl flex items-center justify-center animate-bounce-gentle\">\n          <span className=\"text-3xl font-bold text-white\">S</span>\n        </div>\n        <div className=\"space-y-2\">\n          <h1 className=\"text-4xl font-bold gradient-text\">Salonier</h1>\n          <p className=\"text-gray-600\">{message}</p>\n        </div>\n        <LoadingSpinner size=\"lg\" className=\"mx-auto text-purple-600\" />\n      </div>\n    </div>\n  )\n}\n\ninterface SkeletonProps {\n  className?: string\n}\n\nexport function Skeleton({ className }: SkeletonProps) {\n  return (\n    <div\n      className={cn(\n        'animate-pulse rounded-md bg-gray-200',\n        className\n      )}\n    />\n  )\n}\n\nexport function CardSkeleton() {\n  return (\n    <div className=\"p-6 border rounded-xl bg-white shadow-sm\">\n      <div className=\"space-y-4\">\n        <div className=\"flex items-center space-x-4\">\n          <Skeleton className=\"h-12 w-12 rounded-full\" />\n          <div className=\"space-y-2 flex-1\">\n            <Skeleton className=\"h-4 w-3/4\" />\n            <Skeleton className=\"h-3 w-1/2\" />\n          </div>\n        </div>\n        <div className=\"space-y-2\">\n          <Skeleton className=\"h-3 w-full\" />\n          <Skeleton className=\"h-3 w-5/6\" />\n        </div>\n        <div className=\"flex space-x-2\">\n          <Skeleton className=\"h-8 w-20\" />\n          <Skeleton className=\"h-8 w-24\" />\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport function TableSkeleton({ rows = 5 }: { rows?: number }) {\n  return (\n    <div className=\"space-y-4\">\n      {Array.from({ length: rows }).map((_, i) => (\n        <div key={i} className=\"flex items-center space-x-4 p-4 border rounded-lg\">\n          <Skeleton className=\"h-10 w-10 rounded-full\" />\n          <div className=\"flex-1 space-y-2\">\n            <Skeleton className=\"h-4 w-1/3\" />\n            <Skeleton className=\"h-3 w-1/4\" />\n          </div>\n          <Skeleton className=\"h-4 w-20\" />\n          <Skeleton className=\"h-4 w-16\" />\n          <Skeleton className=\"h-8 w-24\" />\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AAOO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB;IAC5E,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,WAAW,CAAC,KAAK,EAAE;kBACpD,cAAA,8OAAC;YACC,WAAU;YACV,OAAM;YACN,MAAK;YACL,SAAQ;;8BAER,8OAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,QAAO;oBACP,aAAY;;;;;;8BAEd,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,GAAE;;;;;;;;;;;;;;;;;AAKZ;AAMO,SAAS,cAAc,EAAE,UAAU,aAAa,EAAsB;IAC3E,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;;;;;;8BAElD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,8OAAC;4BAAE,WAAU;sCAAiB;;;;;;;;;;;;8BAEhC,8OAAC;oBAAe,MAAK;oBAAK,WAAU;;;;;;;;;;;;;;;;;AAI5C;AAMO,SAAS,SAAS,EAAE,SAAS,EAAiB;IACnD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wCACA;;;;;;AAIR;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAS,WAAU;;;;;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAS,WAAU;;;;;;8CACpB,8OAAC;oCAAS,WAAU;;;;;;;;;;;;;;;;;;8BAGxB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAS,WAAU;;;;;;sCACpB,8OAAC;4BAAS,WAAU;;;;;;;;;;;;8BAEtB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAS,WAAU;;;;;;sCACpB,8OAAC;4BAAS,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK9B;AAEO,SAAS,cAAc,EAAE,OAAO,CAAC,EAAqB;IAC3D,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAK,GAAG,GAAG,CAAC,CAAC,GAAG,kBACpC,8OAAC;gBAAY,WAAU;;kCACrB,8OAAC;wBAAS,WAAU;;;;;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAS,WAAU;;;;;;0CACpB,8OAAC;gCAAS,WAAU;;;;;;;;;;;;kCAEtB,8OAAC;wBAAS,WAAU;;;;;;kCACpB,8OAAC;wBAAS,WAAU;;;;;;kCACpB,8OAAC;wBAAS,WAAU;;;;;;;eARZ;;;;;;;;;;AAalB", "debugId": null}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useRequireAuth } from '@/lib/auth-context'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\nimport { LoadingScreen } from '@/components/ui/loading'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  subtitle?: string\n}\n\nexport function DashboardLayout({ children, title, subtitle }: DashboardLayoutProps) {\n  const { user, isLoading } = useRequireAuth()\n\n  if (isLoading) {\n    return <LoadingScreen message=\"Verificando autenticación...\" />\n  }\n\n  if (!user) {\n    return null // useRequireAuth will redirect to login\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Sidebar />\n      \n      <div className=\"lg:pl-64\">\n        <Header title={title} subtitle={subtitle} />\n        \n        <main className=\"p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAaO,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAwB;IACjF,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEzC,IAAI,WAAW;QACb,qBAAO,8OAAC,mIAAA,CAAA,gBAAa;YAAC,SAAQ;;;;;;IAChC;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,wCAAwC;;IACtD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,UAAO;;;;;0BAER,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAEhC,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 1066, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-border bg-card text-card-foreground shadow-soft\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4EACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1147, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/metric-card.tsx"], "sourcesContent": ["import { Card, CardContent } from '@/components/ui/card'\nimport { cn } from '@/lib/utils'\nimport { LucideIcon } from 'lucide-react'\n\ninterface MetricCardProps {\n  title: string\n  value: string | number\n  change?: {\n    value: number\n    type: 'increase' | 'decrease'\n    period: string\n  }\n  icon: LucideIcon\n  className?: string\n}\n\nexport function MetricCard({ title, value, change, icon: Icon, className }: MetricCardProps) {\n  return (\n    <Card className={cn(\"transition-all duration-200 hover:shadow-medium\", className)}>\n      <CardContent className=\"p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex-1\">\n            <p className=\"text-sm font-medium text-muted-foreground\">{title}</p>\n            <p className=\"text-2xl font-bold text-foreground mt-2\">{value}</p>\n            {change && (\n              <div className=\"flex items-center mt-2\">\n                <span className={cn(\n                  \"text-sm font-medium\",\n                  change.type === 'increase' ? \"text-success\" : \"text-destructive\"\n                )}>\n                  {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%\n                </span>\n                <span className=\"text-sm text-muted-foreground ml-1\">\n                  vs {change.period}\n                </span>\n              </div>\n            )}\n          </div>\n          <div className=\"ml-4\">\n            <div className=\"w-12 h-12 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl flex items-center justify-center\">\n              <Icon className=\"h-6 w-6 text-primary\" />\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAeO,SAAS,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAmB;IACzF,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mDAAmD;kBACrE,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAC1D,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;4BACvD,wBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,uBACA,OAAO,IAAI,KAAK,aAAa,iBAAiB;;4CAE7C,OAAO,IAAI,KAAK,aAAa,MAAM;4CAAK,KAAK,GAAG,CAAC,OAAO,KAAK;4CAAE;;;;;;;kDAElE,8OAAC;wCAAK,WAAU;;4CAAqC;4CAC/C,OAAO,MAAM;;;;;;;;;;;;;;;;;;;kCAKzB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9B", "debugId": null}}, {"offset": {"line": 1265, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { DashboardLayout } from '@/components/layout/dashboard-layout'\nimport { MetricCard } from '@/components/ui/metric-card'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { \n  DollarSign, \n  Users, \n  Palette, \n  TrendingUp,\n  Plus,\n  Calendar,\n  Clock,\n  Star\n} from 'lucide-react'\nimport { mockBusinessMetrics, mockClients, mockRecentServices, getUserById } from '@/data/mockData'\nimport { formatCurrency, timeAgo } from '@/lib/utils'\n\nexport default function DashboardPage() {\n  const metrics = mockBusinessMetrics\n  const recentClients = mockClients.slice(0, 5)\n  const recentServices = mockRecentServices\n\n  return (\n    <DashboardLayout \n      title=\"Dashboard\" \n      subtitle=\"Resumen de tu actividad y métricas clave\"\n    >\n      <div className=\"space-y-6\">\n        {/* Métricas principales */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6\">\n          <MetricCard\n            title=\"Ingresos del Mes\"\n            value={formatCurrency(metrics.revenue)}\n            change={{ value: 12.5, type: 'increase', period: 'mes anterior' }}\n            icon={DollarSign}\n          />\n          <MetricCard\n            title=\"Servicios Realizados\"\n            value={metrics.services}\n            change={{ value: 8.2, type: 'increase', period: 'mes anterior' }}\n            icon={Palette}\n          />\n          <MetricCard\n            title=\"Ticket Promedio\"\n            value={formatCurrency(metrics.averageTicket)}\n            change={{ value: 5.1, type: 'increase', period: 'mes anterior' }}\n            icon={TrendingUp}\n          />\n          <MetricCard\n            title=\"Retención de Clientes\"\n            value={`${metrics.clientRetention}%`}\n            change={{ value: 2.3, type: 'increase', period: 'mes anterior' }}\n            icon={Users}\n          />\n        </div>\n\n        {/* Acciones rápidas */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Acciones Rápidas</CardTitle>\n            <CardDescription>\n              Accede rápidamente a las funciones más utilizadas\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n              <Button className=\"h-16 sm:h-20 flex-col space-y-1 sm:space-y-2\" size=\"lg\">\n                <Plus className=\"h-5 w-5 sm:h-6 sm:w-6\" />\n                <span className=\"text-sm sm:text-base\">Nuevo Servicio</span>\n              </Button>\n              <Button variant=\"outline\" className=\"h-16 sm:h-20 flex-col space-y-1 sm:space-y-2\" size=\"lg\">\n                <Users className=\"h-5 w-5 sm:h-6 sm:w-6\" />\n                <span className=\"text-sm sm:text-base\">Agregar Cliente</span>\n              </Button>\n              <Button variant=\"outline\" className=\"h-16 sm:h-20 flex-col space-y-1 sm:space-y-2\" size=\"lg\">\n                <Palette className=\"h-5 w-5 sm:h-6 sm:w-6\" />\n                <span className=\"text-sm sm:text-base\">Crear Fórmula</span>\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Servicios recientes */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Clock className=\"h-5 w-5 mr-2\" />\n                Servicios Recientes\n              </CardTitle>\n              <CardDescription>\n                Últimos servicios realizados\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {recentServices.map((service) => {\n                  const client = mockClients.find(c => c.id === service.clientId)\n                  const stylist = getUserById(service.performedBy)\n                  \n                  return (\n                    <div key={service.id} className=\"flex items-center justify-between p-3 rounded-lg bg-muted/50\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center\">\n                          <span className=\"text-sm font-medium text-white\">\n                            {client?.name.charAt(0)}\n                          </span>\n                        </div>\n                        <div>\n                          <p className=\"font-medium text-foreground\">{client?.name}</p>\n                          <p className=\"text-sm text-muted-foreground\">\n                            por {stylist?.name} • {timeAgo(service.scheduledAt)}\n                          </p>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <p className=\"font-medium text-foreground\">\n                          {formatCurrency(service.totalCharged)}\n                        </p>\n                        {service.clientRating && (\n                          <div className=\"flex items-center\">\n                            <Star className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                            <span className=\"text-sm text-muted-foreground ml-1\">\n                              {service.clientRating}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )\n                })}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Clientes recientes */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Users className=\"h-5 w-5 mr-2\" />\n                Clientes Recientes\n              </CardTitle>\n              <CardDescription>\n                Últimos clientes agregados\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {recentClients.map((client) => (\n                  <div key={client.id} className=\"flex items-center justify-between p-3 rounded-lg bg-muted/50\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center\">\n                        <span className=\"text-sm font-medium text-white\">\n                          {client.name.charAt(0)}\n                        </span>\n                      </div>\n                      <div>\n                        <p className=\"font-medium text-foreground\">{client.name}</p>\n                        <p className=\"text-sm text-muted-foreground\">\n                          {client.vipStatus && (\n                            <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mr-2\">\n                              VIP\n                            </span>\n                          )}\n                          {client.lastVisit ? timeAgo(client.lastVisit) : 'Nuevo cliente'}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <Button variant=\"ghost\" size=\"sm\">\n                        Ver perfil\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Próximas citas */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <Calendar className=\"h-5 w-5 mr-2\" />\n              Próximas Citas\n            </CardTitle>\n            <CardDescription>\n              Agenda para hoy y mañana\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-center py-8 text-muted-foreground\">\n              <Calendar className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n              <p>No hay citas programadas</p>\n              <Button variant=\"outline\" className=\"mt-4\">\n                Programar Cita\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAjBA;;;;;;;;;AAmBe,SAAS;IACtB,MAAM,UAAU,uHAAA,CAAA,sBAAmB;IACnC,MAAM,gBAAgB,uHAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG;IAC3C,MAAM,iBAAiB,uHAAA,CAAA,qBAAkB;IAEzC,qBACE,8OAAC,mJAAA,CAAA,kBAAe;QACd,OAAM;QACN,UAAS;kBAET,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0IAAA,CAAA,aAAU;4BACT,OAAM;4BACN,OAAO,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,OAAO;4BACrC,QAAQ;gCAAE,OAAO;gCAAM,MAAM;gCAAY,QAAQ;4BAAe;4BAChE,MAAM,kNAAA,CAAA,aAAU;;;;;;sCAElB,8OAAC,0IAAA,CAAA,aAAU;4BACT,OAAM;4BACN,OAAO,QAAQ,QAAQ;4BACvB,QAAQ;gCAAE,OAAO;gCAAK,MAAM;gCAAY,QAAQ;4BAAe;4BAC/D,MAAM,wMAAA,CAAA,UAAO;;;;;;sCAEf,8OAAC,0IAAA,CAAA,aAAU;4BACT,OAAM;4BACN,OAAO,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa;4BAC3C,QAAQ;gCAAE,OAAO;gCAAK,MAAM;gCAAY,QAAQ;4BAAe;4BAC/D,MAAM,kNAAA,CAAA,aAAU;;;;;;sCAElB,8OAAC,0IAAA,CAAA,aAAU;4BACT,OAAM;4BACN,OAAO,GAAG,QAAQ,eAAe,CAAC,CAAC,CAAC;4BACpC,QAAQ;gCAAE,OAAO;gCAAK,MAAM;gCAAY,QAAQ;4BAAe;4BAC/D,MAAM,oMAAA,CAAA,QAAK;;;;;;;;;;;;8BAKf,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;wCAA+C,MAAK;;0DACpE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;;;;;;;kDAEzC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;wCAA+C,MAAK;;0DACtF,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;;;;;;;kDAEzC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;wCAA+C,MAAK;;0DACtF,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAM/C,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC;4CACnB,MAAM,SAAS,uHAAA,CAAA,cAAW,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,QAAQ;4CAC9D,MAAM,UAAU,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,WAAW;4CAE/C,qBACE,8OAAC;gDAAqB,WAAU;;kEAC9B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EACb,QAAQ,KAAK,OAAO;;;;;;;;;;;0EAGzB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA+B,QAAQ;;;;;;kFACpD,8OAAC;wEAAE,WAAU;;4EAAgC;4EACtC,SAAS;4EAAK;4EAAI,CAAA,GAAA,mHAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;kEAIxD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;;;;;;4DAErC,QAAQ,YAAY,kBACnB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;wEAAK,WAAU;kFACb,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;+CAtBrB,QAAQ,EAAE;;;;;wCA6BxB;;;;;;;;;;;;;;;;;sCAMN,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC;gDAAoB,WAAU;;kEAC7B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EACb,OAAO,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;0EAGxB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA+B,OAAO,IAAI;;;;;;kFACvD,8OAAC;wEAAE,WAAU;;4EACV,OAAO,SAAS,kBACf,8OAAC;gFAAK,WAAU;0FAAyG;;;;;;4EAI1H,OAAO,SAAS,GAAG,CAAA,GAAA,mHAAA,CAAA,UAAO,AAAD,EAAE,OAAO,SAAS,IAAI;;;;;;;;;;;;;;;;;;;kEAItD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAAK;;;;;;;;;;;;+CApB5B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAgC7B,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAE;;;;;;kDACH,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD", "debugId": null}}]}