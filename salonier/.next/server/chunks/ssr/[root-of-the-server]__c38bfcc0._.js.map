{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount)\n}\n\nexport function formatPercentage(value: number): string {\n  return `${Math.round(value)}%`\n}\n\nexport function formatTime(minutes: number): string {\n  const hours = Math.floor(minutes / 60)\n  const mins = minutes % 60\n  \n  if (hours > 0) {\n    return `${hours}h ${mins}m`\n  }\n  return `${mins}m`\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function calculateAge(birthDate: Date): number {\n  const today = new Date()\n  const age = today.getFullYear() - birthDate.getFullYear()\n  const monthDiff = today.getMonth() - birthDate.getMonth()\n  \n  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\n    return age - 1\n  }\n  \n  return age\n}\n\nexport function timeAgo(date: Date): string {\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) return 'Hace un momento'\n  if (diffInSeconds < 3600) return `Hace ${Math.floor(diffInSeconds / 60)} min`\n  if (diffInSeconds < 86400) return `Hace ${Math.floor(diffInSeconds / 3600)} h`\n  if (diffInSeconds < 2592000) return `Hace ${Math.floor(diffInSeconds / 86400)} días`\n  if (diffInSeconds < 31536000) return `Hace ${Math.floor(diffInSeconds / 2592000)} meses`\n  return `Hace ${Math.floor(diffInSeconds / 31536000)} años`\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;AAChC;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,OAAO,UAAU;IAEvB,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;IAC7B;IACA,OAAO,GAAG,KAAK,CAAC,CAAC;AACnB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,aAAa,SAAe;IAC1C,MAAM,QAAQ,IAAI;IAClB,MAAM,MAAM,MAAM,WAAW,KAAK,UAAU,WAAW;IACvD,MAAM,YAAY,MAAM,QAAQ,KAAK,UAAU,QAAQ;IAEvD,IAAI,YAAY,KAAM,cAAc,KAAK,MAAM,OAAO,KAAK,UAAU,OAAO,IAAK;QAC/E,OAAO,MAAM;IACf;IAEA,OAAO;AACT;AAEO,SAAS,QAAQ,IAAU;IAChC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC;IAC7E,IAAI,gBAAgB,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,MAAM,EAAE,CAAC;IAC9E,IAAI,gBAAgB,SAAS,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC;IACpF,IAAI,gBAAgB,UAAU,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,SAAS,MAAM,CAAC;IACxF,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,UAAU,KAAK,CAAC;AAC5D;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center rounded-xl font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\"\n    \n    const variants = {\n      primary: \"bg-gradient-to-r from-primary to-secondary text-primary-foreground shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]\",\n      secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n      outline: \"border border-border bg-background hover:bg-muted hover:text-muted-foreground\",\n      ghost: \"hover:bg-muted hover:text-muted-foreground\",\n      destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\"\n    }\n    \n    const sizes = {\n      sm: \"h-9 px-3 text-sm\",\n      md: \"h-11 px-6 text-sm\",\n      lg: \"h-12 px-8 text-base\"\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { useAuth } from '@/lib/auth-context'\nimport { \n  Home, \n  Users, \n  Palette, \n  Package, \n  BarChart3, \n  Timer, \n  Settings,\n  LogOut,\n  Menu,\n  X\n} from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Clientes', href: '/clients', icon: Users },\n  { name: 'Formulación', href: '/formulas', icon: Palette },\n  { name: 'Inventario', href: '/inventory', icon: Package },\n  { name: 'Servicios', href: '/services', icon: Timer },\n  { name: 'Analytics', href: '/analytics', icon: BarChart3 },\n  { name: 'Configuración', href: '/settings', icon: Settings },\n]\n\nexport function Sidebar() {\n  const [isOpen, setIsOpen] = useState(false)\n  const pathname = usePathname()\n  const { user, logout } = useAuth()\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <div className=\"lg:hidden fixed top-4 left-4 z-50\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setIsOpen(!isOpen)}\n          className=\"bg-background/80 backdrop-blur-sm\"\n        >\n          {isOpen ? <X className=\"h-4 w-4\" /> : <Menu className=\"h-4 w-4\" />}\n        </Button>\n      </div>\n\n      {/* Overlay for mobile */}\n      {isOpen && (\n        <div \n          className=\"lg:hidden fixed inset-0 bg-black/50 z-40\"\n          onClick={() => setIsOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={cn(\n        \"fixed inset-y-0 left-0 z-50 w-64 bg-card border-r border-border transform transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:inset-0\",\n        isOpen ? \"translate-x-0\" : \"-translate-x-full\"\n      )}>\n        <div className=\"flex flex-col h-full\">\n          {/* Logo */}\n          <div className=\"flex items-center px-6 py-4 border-b border-border\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center\">\n              <span className=\"text-sm font-bold text-white\">S</span>\n            </div>\n            <span className=\"ml-3 text-xl font-bold gradient-text\">Salonier</span>\n          </div>\n\n          {/* User info */}\n          <div className=\"px-6 py-4 border-b border-border\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.name.charAt(0)}\n                </span>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-foreground truncate\">\n                  {user?.name}\n                </p>\n                <p className=\"text-xs text-muted-foreground truncate\">\n                  {user?.role === 'owner' ? 'Propietario' : \n                   user?.role === 'colorist' ? 'Colorista' : \n                   user?.role === 'admin' ? 'Administrador' : 'Asistente'}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsOpen(false)}\n                  className={cn(\n                    \"group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200\",\n                    isActive\n                      ? \"bg-primary text-primary-foreground shadow-md\"\n                      : \"text-muted-foreground hover:text-foreground hover:bg-muted\"\n                  )}\n                >\n                  <item.icon className={cn(\n                    \"mr-3 h-5 w-5 transition-colors\",\n                    isActive ? \"text-primary-foreground\" : \"text-muted-foreground group-hover:text-foreground\"\n                  )} />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Logout */}\n          <div className=\"px-4 py-4 border-t border-border\">\n            <Button\n              variant=\"ghost\"\n              className=\"w-full justify-start text-muted-foreground hover:text-foreground\"\n              onClick={logout}\n            >\n              <LogOut className=\"mr-3 h-5 w-5\" />\n              Cerrar Sesión\n            </Button>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAnBA;;;;;;;;;AAqBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,mMAAA,CAAA,OAAI;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAY,MAAM,oMAAA,CAAA,QAAK;IAAC;IAClD;QAAE,MAAM;QAAe,MAAM;QAAa,MAAM,wMAAA,CAAA,UAAO;IAAC;IACxD;QAAE,MAAM;QAAc,MAAM;QAAc,MAAM,wMAAA,CAAA,UAAO;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAa,MAAM,oMAAA,CAAA,QAAK;IAAC;IACpD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,YAAS;IAAC;IACzD;QAAE,MAAM;QAAiB,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;IAAC;CAC5D;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAE/B,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,UAAU,CAAC;oBAC1B,WAAU;8BAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAAe,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;YAKzD,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;0BAK7B,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,iKACA,SAAS,kBAAkB;0BAE3B,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,MAAM,KAAK,OAAO;;;;;;;;;;;kDAGvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,MAAM;;;;;;0DAET,8OAAC;gDAAE,WAAU;0DACV,MAAM,SAAS,UAAU,gBACzB,MAAM,SAAS,aAAa,cAC5B,MAAM,SAAS,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;sCAOpD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gGACA,WACI,iDACA;;sDAGN,8OAAC,KAAK,IAAI;4CAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACrB,kCACA,WAAW,4BAA4B;;;;;;wCAExC,KAAK,IAAI;;mCAdL,KAAK,IAAI;;;;;4BAiBpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;;kDAET,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  icon?: React.ReactNode\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, icon, ...props }, ref) => {\n    return (\n      <div className=\"space-y-2\">\n        {label && (\n          <label className=\"text-sm font-medium text-foreground\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {icon && (\n            <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {icon}\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              \"flex h-11 w-full rounded-xl border border-border bg-input px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n              icon && \"pl-10\",\n              error && \"border-destructive focus-visible:ring-destructive\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n        </div>\n        {error && (\n          <p className=\"text-sm text-destructive\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAClD,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;oBACZ,sBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGL,8OAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4VACA,QAAQ,SACR,SAAS,qDACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;;;;;;;YAGZ,uBACC,8OAAC;gBAAE,WAAU;0BAA4B;;;;;;;;;;;;AAIjD;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Bell, Search, Sun, Moon } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { useAuth } from '@/lib/auth-context'\n\ninterface HeaderProps {\n  title: string\n  subtitle?: string\n}\n\nexport function Header({ title, subtitle }: HeaderProps) {\n  const { user, organization } = useAuth()\n\n  return (\n    <header className=\"bg-background/80 backdrop-blur-sm border-b border-border sticky top-0 z-30\">\n      <div className=\"px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Title Section */}\n          <div className=\"flex-1\">\n            <h1 className=\"text-2xl font-bold text-foreground\">{title}</h1>\n            {subtitle && (\n              <p className=\"text-sm text-muted-foreground mt-1\">{subtitle}</p>\n            )}\n          </div>\n\n          {/* Actions Section */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Search */}\n            <div className=\"hidden md:block\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  placeholder=\"Buscar clientes, servicios...\"\n                  className=\"pl-10 w-64\"\n                />\n              </div>\n            </div>\n\n            {/* Notifications */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n              <Bell className=\"h-5 w-5\" />\n              <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full text-xs flex items-center justify-center text-white\">\n                3\n              </span>\n            </Button>\n\n            {/* Theme Toggle */}\n            <Button variant=\"ghost\" size=\"sm\">\n              <Sun className=\"h-5 w-5\" />\n            </Button>\n\n            {/* User Menu */}\n            <div className=\"flex items-center space-x-3 pl-4 border-l border-border\">\n              <div className=\"hidden md:block text-right\">\n                <p className=\"text-sm font-medium text-foreground\">{user?.name}</p>\n                <p className=\"text-xs text-muted-foreground\">{organization?.name}</p>\n              </div>\n              <div className=\"w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.name.charAt(0)}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAe;IACrD,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAErC,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;4BACnD,0BACC,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;;;;;;;kCAKvD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAMhB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;;kDAC1C,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAmH;;;;;;;;;;;;0CAMrI,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAC3B,cAAA,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAIjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAuC,MAAM;;;;;;0DAC1D,8OAAC;gDAAE,WAAU;0DAAiC,cAAc;;;;;;;;;;;;kDAE9D,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,MAAM,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrC", "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useRequireAuth } from '@/lib/auth-context'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\nimport { LoadingScreen } from '@/components/ui/loading'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  subtitle?: string\n}\n\nexport function DashboardLayout({ children, title, subtitle }: DashboardLayoutProps) {\n  const { user, isLoading } = useRequireAuth()\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center\">\n        <div className=\"text-center space-y-4\">\n          <div className=\"mx-auto w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-2xl flex items-center justify-center animate-pulse\">\n            <span className=\"text-2xl font-bold text-white\">S</span>\n          </div>\n          <div>\n            <h1 className=\"text-3xl font-bold gradient-text\"><PERSON><PERSON></h1>\n            <p className=\"text-muted-foreground mt-2\">Cargando...</p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // useRequireAuth will redirect to login\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Sidebar />\n      \n      <div className=\"lg:pl-64\">\n        <Header title={title} subtitle={subtitle} />\n        \n        <main className=\"p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAaO,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAwB;IACjF,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAEzC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;kCAElD,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;IAKpD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,wCAAwC;;IACtD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,UAAO;;;;;0BAER,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAEhC,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-border bg-card text-card-foreground shadow-soft\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4EACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 932, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/app/clients/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { DashboardLayout } from '@/components/layout/dashboard-layout'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport {\n  Search,\n  Plus,\n  Filter,\n  Star,\n  Phone,\n  Mail,\n  Calendar,\n  AlertTriangle\n} from 'lucide-react'\nimport { mockClients } from '@/data/mockData'\nimport { timeAgo } from '@/lib/utils'\n\nexport default function ClientsPage() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedFilter, setSelectedFilter] = useState('all')\n\n  const filteredClients = mockClients.filter(client => {\n    const matchesSearch = client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         client.email?.toLowerCase().includes(searchTerm.toLowerCase())\n    \n    const matchesFilter = selectedFilter === 'all' || \n                         (selectedFilter === 'vip' && client.vipStatus) ||\n                         (selectedFilter === 'new' && !client.lastVisit) ||\n                         (selectedFilter === 'allergies' && client.allergies.length > 0)\n    \n    return matchesSearch && matchesFilter\n  })\n\n  return (\n    <DashboardLayout \n      title=\"Gestión de Clientes\" \n      subtitle=\"Administra tu cartera de clientes y su historial\"\n    >\n      <div className=\"space-y-6\">\n        {/* Header con búsqueda y filtros */}\n        <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n          <div className=\"flex-1 max-w-md\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <Input\n                placeholder=\"Buscar clientes...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n          </div>\n          \n          <div className=\"flex gap-2\">\n            <Button variant=\"outline\" size=\"sm\">\n              <Filter className=\"h-4 w-4 mr-2\" />\n              Filtros\n            </Button>\n            <Button>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Nuevo Cliente\n            </Button>\n          </div>\n        </div>\n\n        {/* Filtros rápidos */}\n        <div className=\"flex gap-2 flex-wrap\">\n          {[\n            { key: 'all', label: 'Todos', count: mockClients.length },\n            { key: 'vip', label: 'VIP', count: mockClients.filter(c => c.vipStatus).length },\n            { key: 'new', label: 'Nuevos', count: mockClients.filter(c => !c.lastVisit).length },\n            { key: 'allergies', label: 'Con Alergias', count: mockClients.filter(c => c.allergies.length > 0).length }\n          ].map((filter) => (\n            <Button\n              key={filter.key}\n              variant={selectedFilter === filter.key ? 'default' : 'outline'}\n              size=\"sm\"\n              onClick={() => setSelectedFilter(filter.key)}\n            >\n              {filter.label} ({filter.count})\n            </Button>\n          ))}\n        </div>\n\n        {/* Lista de clientes */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {filteredClients.map((client) => (\n            <Link key={client.id} href={`/clients/${client.id}`}>\n              <Card className=\"hover:shadow-lg transition-shadow cursor-pointer\">\n              <CardHeader className=\"pb-3\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-400 rounded-full flex items-center justify-center\">\n                      <span className=\"text-lg font-semibold text-white\">\n                        {client.name.charAt(0)}\n                      </span>\n                    </div>\n                    <div>\n                      <CardTitle className=\"text-lg\">{client.name}</CardTitle>\n                      <div className=\"flex items-center gap-2 mt-1\">\n                        {client.vipStatus && (\n                          <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n                            <Star className=\"h-3 w-3 mr-1\" />\n                            VIP\n                          </span>\n                        )}\n                        {client.allergies.length > 0 && (\n                          <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                            <AlertTriangle className=\"h-3 w-3 mr-1\" />\n                            Alergias\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </CardHeader>\n              \n              <CardContent className=\"space-y-3\">\n                {/* Información de contacto */}\n                <div className=\"space-y-2\">\n                  {client.email && (\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <Mail className=\"h-4 w-4 mr-2\" />\n                      {client.email}\n                    </div>\n                  )}\n                  {client.phone && (\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <Phone className=\"h-4 w-4 mr-2\" />\n                      {client.phone}\n                    </div>\n                  )}\n                </div>\n\n                {/* Última visita */}\n                <div className=\"flex items-center text-sm text-gray-600\">\n                  <Calendar className=\"h-4 w-4 mr-2\" />\n                  {client.lastVisit ? (\n                    <span>Última visita: {timeAgo(client.lastVisit)}</span>\n                  ) : (\n                    <span className=\"text-blue-600 font-medium\">Cliente nuevo</span>\n                  )}\n                </div>\n\n                {/* Tags */}\n                {client.tags.length > 0 && (\n                  <div className=\"flex flex-wrap gap-1\">\n                    {client.tags.map((tag) => (\n                      <span\n                        key={tag}\n                        className=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800\"\n                      >\n                        {tag}\n                      </span>\n                    ))}\n                  </div>\n                )}\n\n                {/* Alergias */}\n                {client.allergies.length > 0 && (\n                  <div className=\"pt-2 border-t\">\n                    <p className=\"text-xs font-medium text-red-600 mb-1\">Alergias:</p>\n                    <div className=\"space-y-1\">\n                      {client.allergies.map((allergy) => (\n                        <div key={allergy.id} className=\"text-xs text-red-700\">\n                          • {allergy.name} \n                          <span className={`ml-1 px-1 rounded text-xs ${\n                            allergy.severity === 'high' ? 'bg-red-200' :\n                            allergy.severity === 'medium' ? 'bg-yellow-200' : 'bg-green-200'\n                          }`}>\n                            {allergy.severity === 'high' ? 'Alta' :\n                             allergy.severity === 'medium' ? 'Media' : 'Baja'}\n                          </span>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Acciones */}\n                <div className=\"flex gap-2 pt-3\">\n                  <Button variant=\"outline\" size=\"sm\" className=\"flex-1\">\n                    Ver Perfil\n                  </Button>\n                  <Button size=\"sm\" className=\"flex-1\">\n                    Nuevo Servicio\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n            </Link>\n          ))}\n        </div>\n\n        {/* Estado vacío */}\n        {filteredClients.length === 0 && (\n          <Card>\n            <CardContent className=\"text-center py-12\">\n              <div className=\"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4\">\n                <Search className=\"h-12 w-12 text-gray-400\" />\n              </div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                No se encontraron clientes\n              </h3>\n              <p className=\"text-gray-500 mb-4\">\n                {searchTerm ? \n                  `No hay clientes que coincidan con \"${searchTerm}\"` :\n                  'No hay clientes en esta categoría'\n                }\n              </p>\n              <Button>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Agregar Primer Cliente\n              </Button>\n            </CardContent>\n          </Card>\n        )}\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAnBA;;;;;;;;;;;AAqBe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,kBAAkB,uHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA;QACzC,MAAM,gBAAgB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,OAAO,KAAK,EAAE,cAAc,SAAS,WAAW,WAAW;QAEhF,MAAM,gBAAgB,mBAAmB,SACnB,mBAAmB,SAAS,OAAO,SAAS,IAC5C,mBAAmB,SAAS,CAAC,OAAO,SAAS,IAC7C,mBAAmB,eAAe,OAAO,SAAS,CAAC,MAAM,GAAG;QAElF,OAAO,iBAAiB;IAC1B;IAEA,qBACE,8OAAC,mJAAA,CAAA,kBAAe;QACd,OAAM;QACN,UAAS;kBAET,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;;;;;;sCAKhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,8OAAC,kIAAA,CAAA,SAAM;;sDACL,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;8BAOvC,8OAAC;oBAAI,WAAU;8BACZ;wBACC;4BAAE,KAAK;4BAAO,OAAO;4BAAS,OAAO,uHAAA,CAAA,cAAW,CAAC,MAAM;wBAAC;wBACxD;4BAAE,KAAK;4BAAO,OAAO;4BAAO,OAAO,uHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;wBAAC;wBAC/E;4BAAE,KAAK;4BAAO,OAAO;4BAAU,OAAO,uHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,EAAE,MAAM;wBAAC;wBACnF;4BAAE,KAAK;4BAAa,OAAO;4BAAgB,OAAO,uHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,CAAC,MAAM,GAAG,GAAG,MAAM;wBAAC;qBAC1G,CAAC,GAAG,CAAC,CAAC,uBACL,8OAAC,kIAAA,CAAA,SAAM;4BAEL,SAAS,mBAAmB,OAAO,GAAG,GAAG,YAAY;4BACrD,MAAK;4BACL,SAAS,IAAM,kBAAkB,OAAO,GAAG;;gCAE1C,OAAO,KAAK;gCAAC;gCAAG,OAAO,KAAK;gCAAC;;2BALzB,OAAO,GAAG;;;;;;;;;;8BAWrB,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC,4JAAA,CAAA,UAAI;4BAAiB,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE;sCACjD,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDAChB,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEACb,OAAO,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;kEAGxB,8OAAC;;0EACC,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAW,OAAO,IAAI;;;;;;0EAC3C,8OAAC;gEAAI,WAAU;;oEACZ,OAAO,SAAS,kBACf,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,kMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;oEAIpC,OAAO,SAAS,CAAC,MAAM,GAAG,mBACzB,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,wNAAA,CAAA,gBAAa;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAUxD,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,8OAAC;gDAAI,WAAU;;oDACZ,OAAO,KAAK,kBACX,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf,OAAO,KAAK;;;;;;;oDAGhB,OAAO,KAAK,kBACX,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,OAAO,KAAK;;;;;;;;;;;;;0DAMnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDACnB,OAAO,SAAS,iBACf,8OAAC;;4DAAK;4DAAgB,CAAA,GAAA,mHAAA,CAAA,UAAO,AAAD,EAAE,OAAO,SAAS;;;;;;6EAE9C,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;4CAK/C,OAAO,IAAI,CAAC,MAAM,GAAG,mBACpB,8OAAC;gDAAI,WAAU;0DACZ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,oBAChB,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;4CAUZ,OAAO,SAAS,CAAC,MAAM,GAAG,mBACzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAwC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;kEACZ,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,wBACrB,8OAAC;gEAAqB,WAAU;;oEAAuB;oEAClD,QAAQ,IAAI;kFACf,8OAAC;wEAAK,WAAW,CAAC,0BAA0B,EAC1C,QAAQ,QAAQ,KAAK,SAAS,eAC9B,QAAQ,QAAQ,KAAK,WAAW,kBAAkB,gBAClD;kFACC,QAAQ,QAAQ,KAAK,SAAS,SAC9B,QAAQ,QAAQ,KAAK,WAAW,UAAU;;;;;;;+DAPrC,QAAQ,EAAE;;;;;;;;;;;;;;;;0DAgB5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,MAAK;wDAAK,WAAU;kEAAS;;;;;;kEAGvD,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,WAAU;kEAAS;;;;;;;;;;;;;;;;;;;;;;;;2BAlGhC,OAAO,EAAE;;;;;;;;;;gBA6GvB,gBAAgB,MAAM,KAAK,mBAC1B,8OAAC,gIAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,8OAAC;gCAAE,WAAU;0CACV,aACC,CAAC,mCAAmC,EAAE,WAAW,CAAC,CAAC,GACnD;;;;;;0CAGJ,8OAAC,kIAAA,CAAA,SAAM;;kDACL,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD", "debugId": null}}]}