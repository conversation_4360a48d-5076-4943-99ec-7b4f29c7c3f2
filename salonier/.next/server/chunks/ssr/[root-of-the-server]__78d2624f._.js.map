{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/data/mockData.ts"], "sourcesContent": ["import { \n  User, \n  Organization, \n  Client, \n  Product, \n  Service, \n  Formula,\n  HairAnalysis,\n  BusinessMetrics \n} from '@/types'\n\n// Mock Organizations\nexport const mockOrganizations: Organization[] = [\n  {\n    id: 'org-1',\n    name: '<PERSON><PERSON>',\n    type: 'salon',\n    subscriptionTier: 'pro',\n    settings: {\n      currency: 'USD',\n      timezone: 'America/New_York',\n      businessHours: {\n        monday: { open: '09:00', close: '18:00', closed: false },\n        tuesday: { open: '09:00', close: '18:00', closed: false },\n        wednesday: { open: '09:00', close: '18:00', closed: false },\n        thursday: { open: '09:00', close: '20:00', closed: false },\n        friday: { open: '09:00', close: '20:00', closed: false },\n        saturday: { open: '08:00', close: '17:00', closed: false },\n        sunday: { open: '10:00', close: '16:00', closed: false },\n      },\n      defaultMargin: 65\n    },\n    createdAt: new Date('2024-01-15')\n  }\n]\n\n// Mock Users\nexport const mockUsers: User[] = [\n  {\n    id: 'user-1',\n    email: '<EMAIL>',\n    name: '<PERSON>',\n    role: 'owner',\n    organizationId: 'org-1',\n    avatar: '/avatars/maria.jpg',\n    preferences: {\n      theme: 'light',\n      language: 'es',\n      notifications: true,\n      defaultBrands: ['Wella', 'L\\'Oréal', 'Matrix']\n    },\n    createdAt: new Date('2024-01-15')\n  },\n  {\n    id: 'user-2',\n    email: '<EMAIL>',\n    name: 'Sofía Rodríguez',\n    role: 'colorist',\n    organizationId: 'org-1',\n    avatar: '/avatars/sofia.jpg',\n    preferences: {\n      theme: 'light',\n      language: 'es',\n      notifications: true,\n      defaultBrands: ['Wella', 'Schwarzkopf']\n    },\n    createdAt: new Date('2024-02-01')\n  }\n]\n\n// Mock Clients\nexport const mockClients: Client[] = [\n  {\n    id: 'client-1',\n    organizationId: 'org-1',\n    name: 'Laura García',\n    email: '<EMAIL>',\n    phone: '******-0123',\n    birthDate: new Date('1985-03-15'),\n    allergies: [\n      {\n        id: 'allergy-1',\n        name: 'PPD (Para-fenilendiamina)',\n        severity: 'high',\n        notes: 'Reacción severa en prueba anterior'\n      }\n    ],\n    preferences: {\n      preferredBrands: ['Wella', 'Matrix'],\n      avoidIngredients: ['PPD', 'Ammonia'],\n      maxProcessingTime: 60,\n      priceRange: { min: 80, max: 200 }\n    },\n    tags: ['VIP', 'Sensible'],\n    vipStatus: true,\n    photos: [],\n    notes: 'Cliente muy fiel, prefiere tonos fríos. Siempre puntual.',\n    createdAt: new Date('2024-01-20'),\n    lastVisit: new Date('2024-06-15')\n  },\n  {\n    id: 'client-2',\n    organizationId: 'org-1',\n    name: 'Carmen Ruiz',\n    email: '<EMAIL>',\n    phone: '******-0124',\n    birthDate: new Date('1978-08-22'),\n    allergies: [],\n    preferences: {\n      preferredBrands: ['L\\'Oréal'],\n      avoidIngredients: [],\n      maxProcessingTime: 90,\n      priceRange: { min: 100, max: 300 }\n    },\n    tags: ['Regular'],\n    vipStatus: false,\n    photos: [],\n    notes: 'Le gusta experimentar con colores nuevos.',\n    createdAt: new Date('2024-02-10'),\n    lastVisit: new Date('2024-06-10')\n  },\n  {\n    id: 'client-3',\n    organizationId: 'org-1',\n    name: 'Ana Martínez',\n    email: '<EMAIL>',\n    phone: '******-0125',\n    birthDate: new Date('1992-11-05'),\n    allergies: [],\n    preferences: {\n      preferredBrands: ['Matrix', 'Schwarzkopf'],\n      avoidIngredients: [],\n      maxProcessingTime: 75,\n      priceRange: { min: 60, max: 150 }\n    },\n    tags: ['Nueva'],\n    vipStatus: false,\n    photos: [],\n    notes: 'Primera visita muy exitosa, quiere mantener color natural.',\n    createdAt: new Date('2024-06-01'),\n    lastVisit: new Date('2024-06-20')\n  }\n]\n\n// Mock Products\nexport const mockProducts: Product[] = [\n  {\n    id: 'prod-1',\n    organizationId: 'org-1',\n    brand: 'Wella',\n    line: 'Koleston Perfect',\n    name: '6/0 Rubio Oscuro Natural',\n    code: 'WK-60',\n    type: 'color',\n    currentStock: 12,\n    unitCost: 8.50,\n    reorderPoint: 3,\n    properties: {\n      level: 6,\n      tone: 'natural',\n      size: 60,\n      unit: 'ml',\n      ingredients: ['Etanolamina', 'Resorcinol'],\n      ppd: false\n    },\n    createdAt: new Date('2024-01-15')\n  },\n  {\n    id: 'prod-2',\n    organizationId: 'org-1',\n    brand: 'Wella',\n    line: 'Welloxon Perfect',\n    name: 'Revelador 20 Vol',\n    code: 'WW-20',\n    type: 'developer',\n    currentStock: 8,\n    unitCost: 12.00,\n    reorderPoint: 2,\n    properties: {\n      volume: 20,\n      size: 1000,\n      unit: 'ml',\n      ingredients: ['Peróxido de Hidrógeno'],\n      ppd: false\n    },\n    createdAt: new Date('2024-01-15')\n  },\n  {\n    id: 'prod-3',\n    organizationId: 'org-1',\n    brand: 'Matrix',\n    line: 'SoColor',\n    name: '7A Rubio Medio Ceniza',\n    code: 'MS-7A',\n    type: 'color',\n    currentStock: 15,\n    unitCost: 9.25,\n    reorderPoint: 4,\n    properties: {\n      level: 7,\n      tone: 'ash',\n      size: 90,\n      unit: 'ml',\n      ingredients: ['MEA', 'Resorcinol'],\n      ppd: false\n    },\n    createdAt: new Date('2024-01-20')\n  }\n]\n\n// Mock Business Metrics\nexport const mockBusinessMetrics: BusinessMetrics = {\n  period: {\n    start: new Date('2024-06-01'),\n    end: new Date('2024-06-30')\n  },\n  revenue: 12450,\n  services: 89,\n  averageTicket: 139.89,\n  profitMargin: 68.5,\n  clientRetention: 85.2,\n  topServices: [\n    { name: 'Balayage Completo', count: 23, revenue: 4140, margin: 72.1 },\n    { name: 'Retoque de Raíz', count: 31, revenue: 2790, margin: 78.5 },\n    { name: 'Corrección de Color', count: 12, revenue: 2880, margin: 65.3 },\n    { name: 'Mechas Tradicionales', count: 18, revenue: 2340, margin: 69.8 },\n    { name: 'Tinte Completo', count: 5, revenue: 300, margin: 75.0 }\n  ],\n  topProducts: [\n    { name: 'Wella Koleston 6/0', usage: 450, cost: 38.25, efficiency: 92.3 },\n    { name: 'Matrix SoColor 7A', usage: 380, cost: 35.15, efficiency: 89.7 },\n    { name: 'Revelador 20 Vol', usage: 2100, cost: 25.20, efficiency: 95.1 },\n    { name: 'Olaplex No.1', usage: 125, cost: 62.50, efficiency: 88.4 }\n  ]\n}\n\n// Mock Recent Services\nexport const mockRecentServices: Service[] = [\n  {\n    id: 'service-1',\n    clientId: 'client-1',\n    formulaId: 'formula-1',\n    performedBy: 'user-2',\n    scheduledAt: new Date('2024-06-24T10:00:00'),\n    startedAt: new Date('2024-06-24T10:15:00'),\n    completedAt: new Date('2024-06-24T12:30:00'),\n    processNotes: [\n      {\n        time: new Date('2024-06-24T10:15:00'),\n        note: 'Iniciando aplicación en raíz',\n        type: 'system',\n        zone: 'roots'\n      },\n      {\n        time: new Date('2024-06-24T10:45:00'),\n        note: 'Cliente cómoda, sin irritación',\n        type: 'voice'\n      }\n    ],\n    photos: [],\n    totalCharged: 145.00,\n    paymentMethod: 'card',\n    clientRating: 5,\n    clientFeedback: '¡Perfecto como siempre! Me encanta el resultado.',\n    createdAt: new Date('2024-06-24T10:00:00')\n  }\n]\n\n// Helper functions for mock data\nexport function getClientById(id: string): Client | undefined {\n  return mockClients.find(client => client.id === id)\n}\n\nexport function getUserById(id: string): User | undefined {\n  return mockUsers.find(user => user.id === id)\n}\n\nexport function getProductById(id: string): Product | undefined {\n  return mockProducts.find(product => product.id === id)\n}\n\nexport function getClientsByOrganization(orgId: string): Client[] {\n  return mockClients.filter(client => client.organizationId === orgId)\n}\n\nexport function getProductsByOrganization(orgId: string): Product[] {\n  return mockProducts.filter(product => product.organizationId === orgId)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAYO,MAAM,oBAAoC;IAC/C;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,kBAAkB;QAClB,UAAU;YACR,UAAU;YACV,UAAU;YACV,eAAe;gBACb,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACvD,SAAS;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACxD,WAAW;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBAC1D,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACzD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACvD,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACzD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;YACzD;YACA,eAAe;QACjB;QACA,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,YAAoB;IAC/B;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,gBAAgB;QAChB,QAAQ;QACR,aAAa;YACX,OAAO;YACP,UAAU;YACV,eAAe;YACf,eAAe;gBAAC;gBAAS;gBAAY;aAAS;QAChD;QACA,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,gBAAgB;QAChB,QAAQ;QACR,aAAa;YACX,OAAO;YACP,UAAU;YACV,eAAe;YACf,eAAe;gBAAC;gBAAS;aAAc;QACzC;QACA,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,cAAwB;IACnC;QACE,IAAI;QACJ,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,OAAO;YACT;SACD;QACD,aAAa;YACX,iBAAiB;gBAAC;gBAAS;aAAS;YACpC,kBAAkB;gBAAC;gBAAO;aAAU;YACpC,mBAAmB;YACnB,YAAY;gBAAE,KAAK;gBAAI,KAAK;YAAI;QAClC;QACA,MAAM;YAAC;YAAO;SAAW;QACzB,WAAW;QACX,QAAQ,EAAE;QACV,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,EAAE;QACb,aAAa;YACX,iBAAiB;gBAAC;aAAW;YAC7B,kBAAkB,EAAE;YACpB,mBAAmB;YACnB,YAAY;gBAAE,KAAK;gBAAK,KAAK;YAAI;QACnC;QACA,MAAM;YAAC;SAAU;QACjB,WAAW;QACX,QAAQ,EAAE;QACV,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,EAAE;QACb,aAAa;YACX,iBAAiB;gBAAC;gBAAU;aAAc;YAC1C,kBAAkB,EAAE;YACpB,mBAAmB;YACnB,YAAY;gBAAE,KAAK;gBAAI,KAAK;YAAI;QAClC;QACA,MAAM;YAAC;SAAQ;QACf,WAAW;QACX,QAAQ,EAAE;QACV,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,gBAAgB;QAChB,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,cAAc;QACd,UAAU;QACV,cAAc;QACd,YAAY;YACV,OAAO;YACP,MAAM;YACN,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;gBAAe;aAAa;YAC1C,KAAK;QACP;QACA,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,gBAAgB;QAChB,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,cAAc;QACd,UAAU;QACV,cAAc;QACd,YAAY;YACV,QAAQ;YACR,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;aAAwB;YACtC,KAAK;QACP;QACA,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,gBAAgB;QAChB,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,cAAc;QACd,UAAU;QACV,cAAc;QACd,YAAY;YACV,OAAO;YACP,MAAM;YACN,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;gBAAO;aAAa;YAClC,KAAK;QACP;QACA,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,sBAAuC;IAClD,QAAQ;QACN,OAAO,IAAI,KAAK;QAChB,KAAK,IAAI,KAAK;IAChB;IACA,SAAS;IACT,UAAU;IACV,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,aAAa;QACX;YAAE,MAAM;YAAqB,OAAO;YAAI,SAAS;YAAM,QAAQ;QAAK;QACpE;YAAE,MAAM;YAAmB,OAAO;YAAI,SAAS;YAAM,QAAQ;QAAK;QAClE;YAAE,MAAM;YAAuB,OAAO;YAAI,SAAS;YAAM,QAAQ;QAAK;QACtE;YAAE,MAAM;YAAwB,OAAO;YAAI,SAAS;YAAM,QAAQ;QAAK;QACvE;YAAE,MAAM;YAAkB,OAAO;YAAG,SAAS;YAAK,QAAQ;QAAK;KAChE;IACD,aAAa;QACX;YAAE,MAAM;YAAsB,OAAO;YAAK,MAAM;YAAO,YAAY;QAAK;QACxE;YAAE,MAAM;YAAqB,OAAO;YAAK,MAAM;YAAO,YAAY;QAAK;QACvE;YAAE,MAAM;YAAoB,OAAO;YAAM,MAAM;YAAO,YAAY;QAAK;QACvE;YAAE,MAAM;YAAgB,OAAO;YAAK,MAAM;YAAO,YAAY;QAAK;KACnE;AACH;AAGO,MAAM,qBAAgC;IAC3C;QACE,IAAI;QACJ,UAAU;QACV,WAAW;QACX,aAAa;QACb,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;QACpB,aAAa,IAAI,KAAK;QACtB,cAAc;YACZ;gBACE,MAAM,IAAI,KAAK;gBACf,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM,IAAI,KAAK;gBACf,MAAM;gBACN,MAAM;YACR;SACD;QACD,QAAQ,EAAE;QACV,cAAc;QACd,eAAe;QACf,cAAc;QACd,gBAAgB;QAChB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,SAAS,cAAc,EAAU;IACtC,OAAO,YAAY,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AAClD;AAEO,SAAS,YAAY,EAAU;IACpC,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AAC5C;AAEO,SAAS,eAAe,EAAU;IACvC,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACrD;AAEO,SAAS,yBAAyB,KAAa;IACpD,OAAO,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,cAAc,KAAK;AAChE;AAEO,SAAS,0BAA0B,KAAa;IACrD,OAAO,aAAa,MAAM,CAAC,CAAA,UAAW,QAAQ,cAAc,KAAK;AACnE", "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/lib/auth-context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect } from 'react'\nimport { User, Organization } from '@/types'\nimport { mockUsers, mockOrganizations } from '@/data/mockData'\n\ninterface AuthContextType {\n  user: User | null\n  organization: Organization | null\n  isLoading: boolean\n  login: (email: string, password: string) => Promise<boolean>\n  logout: () => void\n  isAuthenticated: boolean\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [organization, setOrganization] = useState<Organization | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    // Simular carga inicial - verificar si hay usuario guardado\n    const savedUser = localStorage.getItem('salonier-user')\n    if (savedUser) {\n      try {\n        const userData = JSON.parse(savedUser)\n        const foundUser = mockUsers.find(u => u.id === userData.id)\n        if (foundUser) {\n          setUser(foundUser)\n          const userOrg = mockOrganizations.find(org => org.id === foundUser.organizationId)\n          setOrganization(userOrg || null)\n        }\n      } catch (error) {\n        console.error('Error loading saved user:', error)\n        localStorage.removeItem('salonier-user')\n      }\n    }\n    setIsLoading(false)\n  }, [])\n\n  const login = async (email: string, password: string): Promise<boolean> => {\n    setIsLoading(true)\n    \n    // Simular delay de autenticación\n    await new Promise(resolve => setTimeout(resolve, 1000))\n    \n    // Buscar usuario en datos mock\n    const foundUser = mockUsers.find(u => u.email === email)\n    \n    if (foundUser && password === 'demo123') { // Password mock para demo\n      setUser(foundUser)\n      const userOrg = mockOrganizations.find(org => org.id === foundUser.organizationId)\n      setOrganization(userOrg || null)\n      \n      // Guardar en localStorage\n      localStorage.setItem('salonier-user', JSON.stringify({ id: foundUser.id }))\n      \n      setIsLoading(false)\n      return true\n    }\n    \n    setIsLoading(false)\n    return false\n  }\n\n  const logout = () => {\n    setUser(null)\n    setOrganization(null)\n    localStorage.removeItem('salonier-user')\n  }\n\n  const value: AuthContextType = {\n    user,\n    organization,\n    isLoading,\n    login,\n    logout,\n    isAuthenticated: !!user\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\n// Hook para proteger rutas\nexport function useRequireAuth() {\n  const { user, isLoading } = useAuth()\n  \n  useEffect(() => {\n    if (!isLoading && !user) {\n      window.location.href = '/auth/login'\n    }\n  }, [user, isLoading])\n  \n  return { user, isLoading }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AAJA;;;;AAeA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4DAA4D;QAC5D,MAAM,YAAY,aAAa,OAAO,CAAC;QACvC,IAAI,WAAW;YACb,IAAI;gBACF,MAAM,WAAW,KAAK,KAAK,CAAC;gBAC5B,MAAM,YAAY,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,EAAE;gBAC1D,IAAI,WAAW;oBACb,QAAQ;oBACR,MAAM,UAAU,uHAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,UAAU,cAAc;oBACjF,gBAAgB,WAAW;gBAC7B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,aAAa,UAAU,CAAC;YAC1B;QACF;QACA,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,aAAa;QAEb,iCAAiC;QACjC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,+BAA+B;QAC/B,MAAM,YAAY,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QAElD,IAAI,aAAa,aAAa,WAAW;YACvC,QAAQ;YACR,MAAM,UAAU,uHAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,UAAU,cAAc;YACjF,gBAAgB,WAAW;YAE3B,0BAA0B;YAC1B,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;gBAAE,IAAI,UAAU,EAAE;YAAC;YAExE,aAAa;YACb,OAAO;QACT;QAEA,aAAa;QACb,OAAO;IACT;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,gBAAgB;QAChB,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,MAAM;YACvB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF,GAAG;QAAC;QAAM;KAAU;IAEpB,OAAO;QAAE;QAAM;IAAU;AAC3B", "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}