module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/data/mockData.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getClientById": (()=>getClientById),
    "getClientsByOrganization": (()=>getClientsByOrganization),
    "getProductById": (()=>getProductById),
    "getProductsByOrganization": (()=>getProductsByOrganization),
    "getUserById": (()=>getUserById),
    "mockBusinessMetrics": (()=>mockBusinessMetrics),
    "mockClients": (()=>mockClients),
    "mockOrganizations": (()=>mockOrganizations),
    "mockProducts": (()=>mockProducts),
    "mockRecentServices": (()=>mockRecentServices),
    "mockUsers": (()=>mockUsers)
});
const mockOrganizations = [
    {
        id: 'org-1',
        name: 'Salón Belleza María',
        type: 'salon',
        subscriptionTier: 'pro',
        settings: {
            currency: 'USD',
            timezone: 'America/New_York',
            businessHours: {
                monday: {
                    open: '09:00',
                    close: '18:00',
                    closed: false
                },
                tuesday: {
                    open: '09:00',
                    close: '18:00',
                    closed: false
                },
                wednesday: {
                    open: '09:00',
                    close: '18:00',
                    closed: false
                },
                thursday: {
                    open: '09:00',
                    close: '20:00',
                    closed: false
                },
                friday: {
                    open: '09:00',
                    close: '20:00',
                    closed: false
                },
                saturday: {
                    open: '08:00',
                    close: '17:00',
                    closed: false
                },
                sunday: {
                    open: '10:00',
                    close: '16:00',
                    closed: false
                }
            },
            defaultMargin: 65
        },
        createdAt: new Date('2024-01-15')
    }
];
const mockUsers = [
    {
        id: 'user-1',
        email: '<EMAIL>',
        name: 'María González',
        role: 'owner',
        organizationId: 'org-1',
        avatar: '/avatars/maria.jpg',
        preferences: {
            theme: 'light',
            language: 'es',
            notifications: true,
            defaultBrands: [
                'Wella',
                'L\'Oréal',
                'Matrix'
            ]
        },
        createdAt: new Date('2024-01-15')
    },
    {
        id: 'user-2',
        email: '<EMAIL>',
        name: 'Sofía Rodríguez',
        role: 'colorist',
        organizationId: 'org-1',
        avatar: '/avatars/sofia.jpg',
        preferences: {
            theme: 'light',
            language: 'es',
            notifications: true,
            defaultBrands: [
                'Wella',
                'Schwarzkopf'
            ]
        },
        createdAt: new Date('2024-02-01')
    }
];
const mockClients = [
    {
        id: 'client-1',
        organizationId: 'org-1',
        name: 'Laura García',
        email: '<EMAIL>',
        phone: '******-0123',
        birthDate: new Date('1985-03-15'),
        allergies: [
            {
                id: 'allergy-1',
                name: 'PPD (Para-fenilendiamina)',
                severity: 'high',
                notes: 'Reacción severa en prueba anterior'
            }
        ],
        preferences: {
            preferredBrands: [
                'Wella',
                'Matrix'
            ],
            avoidIngredients: [
                'PPD',
                'Ammonia'
            ],
            maxProcessingTime: 60,
            priceRange: {
                min: 80,
                max: 200
            }
        },
        tags: [
            'VIP',
            'Sensible'
        ],
        vipStatus: true,
        photos: [],
        notes: 'Cliente muy fiel, prefiere tonos fríos. Siempre puntual.',
        createdAt: new Date('2024-01-20'),
        lastVisit: new Date('2024-06-15')
    },
    {
        id: 'client-2',
        organizationId: 'org-1',
        name: 'Carmen Ruiz',
        email: '<EMAIL>',
        phone: '******-0124',
        birthDate: new Date('1978-08-22'),
        allergies: [],
        preferences: {
            preferredBrands: [
                'L\'Oréal'
            ],
            avoidIngredients: [],
            maxProcessingTime: 90,
            priceRange: {
                min: 100,
                max: 300
            }
        },
        tags: [
            'Regular'
        ],
        vipStatus: false,
        photos: [],
        notes: 'Le gusta experimentar con colores nuevos.',
        createdAt: new Date('2024-02-10'),
        lastVisit: new Date('2024-06-10')
    },
    {
        id: 'client-3',
        organizationId: 'org-1',
        name: 'Ana Martínez',
        email: '<EMAIL>',
        phone: '******-0125',
        birthDate: new Date('1992-11-05'),
        allergies: [],
        preferences: {
            preferredBrands: [
                'Matrix',
                'Schwarzkopf'
            ],
            avoidIngredients: [],
            maxProcessingTime: 75,
            priceRange: {
                min: 60,
                max: 150
            }
        },
        tags: [
            'Nueva'
        ],
        vipStatus: false,
        photos: [],
        notes: 'Primera visita muy exitosa, quiere mantener color natural.',
        createdAt: new Date('2024-06-01'),
        lastVisit: new Date('2024-06-20')
    }
];
const mockProducts = [
    {
        id: 'prod-1',
        organizationId: 'org-1',
        brand: 'Wella',
        line: 'Koleston Perfect',
        name: '6/0 Rubio Oscuro Natural',
        code: 'WK-60',
        type: 'color',
        currentStock: 12,
        unitCost: 8.50,
        reorderPoint: 3,
        properties: {
            level: 6,
            tone: 'natural',
            size: 60,
            unit: 'ml',
            ingredients: [
                'Etanolamina',
                'Resorcinol'
            ],
            ppd: false
        },
        createdAt: new Date('2024-01-15')
    },
    {
        id: 'prod-2',
        organizationId: 'org-1',
        brand: 'Wella',
        line: 'Welloxon Perfect',
        name: 'Revelador 20 Vol',
        code: 'WW-20',
        type: 'developer',
        currentStock: 8,
        unitCost: 12.00,
        reorderPoint: 2,
        properties: {
            volume: 20,
            size: 1000,
            unit: 'ml',
            ingredients: [
                'Peróxido de Hidrógeno'
            ],
            ppd: false
        },
        createdAt: new Date('2024-01-15')
    },
    {
        id: 'prod-3',
        organizationId: 'org-1',
        brand: 'Matrix',
        line: 'SoColor',
        name: '7A Rubio Medio Ceniza',
        code: 'MS-7A',
        type: 'color',
        currentStock: 15,
        unitCost: 9.25,
        reorderPoint: 4,
        properties: {
            level: 7,
            tone: 'ash',
            size: 90,
            unit: 'ml',
            ingredients: [
                'MEA',
                'Resorcinol'
            ],
            ppd: false
        },
        createdAt: new Date('2024-01-20')
    }
];
const mockBusinessMetrics = {
    period: {
        start: new Date('2024-06-01'),
        end: new Date('2024-06-30')
    },
    revenue: 12450,
    services: 89,
    averageTicket: 139.89,
    profitMargin: 68.5,
    clientRetention: 85.2,
    topServices: [
        {
            name: 'Balayage Completo',
            count: 23,
            revenue: 4140,
            margin: 72.1
        },
        {
            name: 'Retoque de Raíz',
            count: 31,
            revenue: 2790,
            margin: 78.5
        },
        {
            name: 'Corrección de Color',
            count: 12,
            revenue: 2880,
            margin: 65.3
        },
        {
            name: 'Mechas Tradicionales',
            count: 18,
            revenue: 2340,
            margin: 69.8
        },
        {
            name: 'Tinte Completo',
            count: 5,
            revenue: 300,
            margin: 75.0
        }
    ],
    topProducts: [
        {
            name: 'Wella Koleston 6/0',
            usage: 450,
            cost: 38.25,
            efficiency: 92.3
        },
        {
            name: 'Matrix SoColor 7A',
            usage: 380,
            cost: 35.15,
            efficiency: 89.7
        },
        {
            name: 'Revelador 20 Vol',
            usage: 2100,
            cost: 25.20,
            efficiency: 95.1
        },
        {
            name: 'Olaplex No.1',
            usage: 125,
            cost: 62.50,
            efficiency: 88.4
        }
    ]
};
const mockRecentServices = [
    {
        id: 'service-1',
        clientId: 'client-1',
        formulaId: 'formula-1',
        performedBy: 'user-2',
        scheduledAt: new Date('2024-06-24T10:00:00'),
        startedAt: new Date('2024-06-24T10:15:00'),
        completedAt: new Date('2024-06-24T12:30:00'),
        processNotes: [
            {
                time: new Date('2024-06-24T10:15:00'),
                note: 'Iniciando aplicación en raíz',
                type: 'system',
                zone: 'roots'
            },
            {
                time: new Date('2024-06-24T10:45:00'),
                note: 'Cliente cómoda, sin irritación',
                type: 'voice'
            }
        ],
        photos: [],
        totalCharged: 145.00,
        paymentMethod: 'card',
        clientRating: 5,
        clientFeedback: '¡Perfecto como siempre! Me encanta el resultado.',
        createdAt: new Date('2024-06-24T10:00:00')
    }
];
function getClientById(id) {
    return mockClients.find((client)=>client.id === id);
}
function getUserById(id) {
    return mockUsers.find((user)=>user.id === id);
}
function getProductById(id) {
    return mockProducts.find((product)=>product.id === id);
}
function getClientsByOrganization(orgId) {
    return mockClients.filter((client)=>client.organizationId === orgId);
}
function getProductsByOrganization(orgId) {
    return mockProducts.filter((product)=>product.organizationId === orgId);
}
}}),
"[project]/src/lib/auth-context.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth),
    "useRequireAuth": (()=>useRequireAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$mockData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/mockData.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [organization, setOrganization] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Simular carga inicial - verificar si hay usuario guardado
        const savedUser = localStorage.getItem('salonier-user');
        if (savedUser) {
            try {
                const userData = JSON.parse(savedUser);
                const foundUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$mockData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockUsers"].find((u)=>u.id === userData.id);
                if (foundUser) {
                    setUser(foundUser);
                    const userOrg = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$mockData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockOrganizations"].find((org)=>org.id === foundUser.organizationId);
                    setOrganization(userOrg || null);
                }
            } catch (error) {
                console.error('Error loading saved user:', error);
                localStorage.removeItem('salonier-user');
            }
        }
        setIsLoading(false);
    }, []);
    const login = async (email, password)=>{
        setIsLoading(true);
        // Simular delay de autenticación
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        // Buscar usuario en datos mock
        const foundUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$mockData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockUsers"].find((u)=>u.email === email);
        if (foundUser && password === 'demo123') {
            setUser(foundUser);
            const userOrg = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$mockData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockOrganizations"].find((org)=>org.id === foundUser.organizationId);
            setOrganization(userOrg || null);
            // Guardar en localStorage
            localStorage.setItem('salonier-user', JSON.stringify({
                id: foundUser.id
            }));
            setIsLoading(false);
            return true;
        }
        setIsLoading(false);
        return false;
    };
    const logout = ()=>{
        setUser(null);
        setOrganization(null);
        localStorage.removeItem('salonier-user');
    };
    const value = {
        user,
        organization,
        isLoading,
        login,
        logout,
        isAuthenticated: !!user
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/auth-context.tsx",
        lineNumber: 85,
        columnNumber: 5
    }, this);
}
function useAuth() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
function useRequireAuth() {
    const { user, isLoading } = useAuth();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isLoading && !user) {
            window.location.href = '/auth/login';
        }
    }, [
        user,
        isLoading
    ]);
    return {
        user,
        isLoading
    };
}
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else {
                "TURBOPACK unreachable";
            }
        } else {
            "TURBOPACK unreachable";
        }
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__78d2624f._.js.map