(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/data/mockData.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getClientById": (()=>getClientById),
    "getClientsByOrganization": (()=>getClientsByOrganization),
    "getProductById": (()=>getProductById),
    "getProductsByOrganization": (()=>getProductsByOrganization),
    "getUserById": (()=>getUserById),
    "mockBusinessMetrics": (()=>mockBusinessMetrics),
    "mockClients": (()=>mockClients),
    "mockOrganizations": (()=>mockOrganizations),
    "mockProducts": (()=>mockProducts),
    "mockRecentServices": (()=>mockRecentServices),
    "mockUsers": (()=>mockUsers)
});
const mockOrganizations = [
    {
        id: 'org-1',
        name: 'Salón Belleza María',
        type: 'salon',
        subscriptionTier: 'pro',
        settings: {
            currency: 'USD',
            timezone: 'America/New_York',
            businessHours: {
                monday: {
                    open: '09:00',
                    close: '18:00',
                    closed: false
                },
                tuesday: {
                    open: '09:00',
                    close: '18:00',
                    closed: false
                },
                wednesday: {
                    open: '09:00',
                    close: '18:00',
                    closed: false
                },
                thursday: {
                    open: '09:00',
                    close: '20:00',
                    closed: false
                },
                friday: {
                    open: '09:00',
                    close: '20:00',
                    closed: false
                },
                saturday: {
                    open: '08:00',
                    close: '17:00',
                    closed: false
                },
                sunday: {
                    open: '10:00',
                    close: '16:00',
                    closed: false
                }
            },
            defaultMargin: 65
        },
        createdAt: new Date('2024-01-15')
    }
];
const mockUsers = [
    {
        id: 'user-1',
        email: '<EMAIL>',
        name: 'María González',
        role: 'owner',
        organizationId: 'org-1',
        avatar: '/avatars/maria.jpg',
        preferences: {
            theme: 'light',
            language: 'es',
            notifications: true,
            defaultBrands: [
                'Wella',
                'L\'Oréal',
                'Matrix'
            ]
        },
        createdAt: new Date('2024-01-15')
    },
    {
        id: 'user-2',
        email: '<EMAIL>',
        name: 'Sofía Rodríguez',
        role: 'colorist',
        organizationId: 'org-1',
        avatar: '/avatars/sofia.jpg',
        preferences: {
            theme: 'light',
            language: 'es',
            notifications: true,
            defaultBrands: [
                'Wella',
                'Schwarzkopf'
            ]
        },
        createdAt: new Date('2024-02-01')
    }
];
const mockClients = [
    {
        id: 'client-1',
        organizationId: 'org-1',
        name: 'Laura García',
        email: '<EMAIL>',
        phone: '******-0123',
        birthDate: new Date('1985-03-15'),
        allergies: [
            {
                id: 'allergy-1',
                name: 'PPD (Para-fenilendiamina)',
                severity: 'high',
                notes: 'Reacción severa en prueba anterior'
            }
        ],
        preferences: {
            preferredBrands: [
                'Wella',
                'Matrix'
            ],
            avoidIngredients: [
                'PPD',
                'Ammonia'
            ],
            maxProcessingTime: 60,
            priceRange: {
                min: 80,
                max: 200
            }
        },
        tags: [
            'VIP',
            'Sensible'
        ],
        vipStatus: true,
        photos: [],
        notes: 'Cliente muy fiel, prefiere tonos fríos. Siempre puntual.',
        createdAt: new Date('2024-01-20'),
        lastVisit: new Date('2024-06-15')
    },
    {
        id: 'client-2',
        organizationId: 'org-1',
        name: 'Carmen Ruiz',
        email: '<EMAIL>',
        phone: '******-0124',
        birthDate: new Date('1978-08-22'),
        allergies: [],
        preferences: {
            preferredBrands: [
                'L\'Oréal'
            ],
            avoidIngredients: [],
            maxProcessingTime: 90,
            priceRange: {
                min: 100,
                max: 300
            }
        },
        tags: [
            'Regular'
        ],
        vipStatus: false,
        photos: [],
        notes: 'Le gusta experimentar con colores nuevos.',
        createdAt: new Date('2024-02-10'),
        lastVisit: new Date('2024-06-10')
    },
    {
        id: 'client-3',
        organizationId: 'org-1',
        name: 'Ana Martínez',
        email: '<EMAIL>',
        phone: '******-0125',
        birthDate: new Date('1992-11-05'),
        allergies: [],
        preferences: {
            preferredBrands: [
                'Matrix',
                'Schwarzkopf'
            ],
            avoidIngredients: [],
            maxProcessingTime: 75,
            priceRange: {
                min: 60,
                max: 150
            }
        },
        tags: [
            'Nueva'
        ],
        vipStatus: false,
        photos: [],
        notes: 'Primera visita muy exitosa, quiere mantener color natural.',
        createdAt: new Date('2024-06-01'),
        lastVisit: new Date('2024-06-20')
    }
];
const mockProducts = [
    {
        id: 'prod-1',
        organizationId: 'org-1',
        brand: 'Wella',
        line: 'Koleston Perfect',
        name: '6/0 Rubio Oscuro Natural',
        code: 'WK-60',
        type: 'color',
        currentStock: 12,
        unitCost: 8.50,
        reorderPoint: 3,
        properties: {
            level: 6,
            tone: 'natural',
            size: 60,
            unit: 'ml',
            ingredients: [
                'Etanolamina',
                'Resorcinol'
            ],
            ppd: false
        },
        createdAt: new Date('2024-01-15')
    },
    {
        id: 'prod-2',
        organizationId: 'org-1',
        brand: 'Wella',
        line: 'Welloxon Perfect',
        name: 'Revelador 20 Vol',
        code: 'WW-20',
        type: 'developer',
        currentStock: 8,
        unitCost: 12.00,
        reorderPoint: 2,
        properties: {
            volume: 20,
            size: 1000,
            unit: 'ml',
            ingredients: [
                'Peróxido de Hidrógeno'
            ],
            ppd: false
        },
        createdAt: new Date('2024-01-15')
    },
    {
        id: 'prod-3',
        organizationId: 'org-1',
        brand: 'Matrix',
        line: 'SoColor',
        name: '7A Rubio Medio Ceniza',
        code: 'MS-7A',
        type: 'color',
        currentStock: 15,
        unitCost: 9.25,
        reorderPoint: 4,
        properties: {
            level: 7,
            tone: 'ash',
            size: 90,
            unit: 'ml',
            ingredients: [
                'MEA',
                'Resorcinol'
            ],
            ppd: false
        },
        createdAt: new Date('2024-01-20')
    }
];
const mockBusinessMetrics = {
    period: {
        start: new Date('2024-06-01'),
        end: new Date('2024-06-30')
    },
    revenue: 12450,
    services: 89,
    averageTicket: 139.89,
    profitMargin: 68.5,
    clientRetention: 85.2,
    topServices: [
        {
            name: 'Balayage Completo',
            count: 23,
            revenue: 4140,
            margin: 72.1
        },
        {
            name: 'Retoque de Raíz',
            count: 31,
            revenue: 2790,
            margin: 78.5
        },
        {
            name: 'Corrección de Color',
            count: 12,
            revenue: 2880,
            margin: 65.3
        },
        {
            name: 'Mechas Tradicionales',
            count: 18,
            revenue: 2340,
            margin: 69.8
        },
        {
            name: 'Tinte Completo',
            count: 5,
            revenue: 300,
            margin: 75.0
        }
    ],
    topProducts: [
        {
            name: 'Wella Koleston 6/0',
            usage: 450,
            cost: 38.25,
            efficiency: 92.3
        },
        {
            name: 'Matrix SoColor 7A',
            usage: 380,
            cost: 35.15,
            efficiency: 89.7
        },
        {
            name: 'Revelador 20 Vol',
            usage: 2100,
            cost: 25.20,
            efficiency: 95.1
        },
        {
            name: 'Olaplex No.1',
            usage: 125,
            cost: 62.50,
            efficiency: 88.4
        }
    ]
};
const mockRecentServices = [
    {
        id: 'service-1',
        clientId: 'client-1',
        formulaId: 'formula-1',
        performedBy: 'user-2',
        scheduledAt: new Date('2024-06-24T10:00:00'),
        startedAt: new Date('2024-06-24T10:15:00'),
        completedAt: new Date('2024-06-24T12:30:00'),
        processNotes: [
            {
                time: new Date('2024-06-24T10:15:00'),
                note: 'Iniciando aplicación en raíz',
                type: 'system',
                zone: 'roots'
            },
            {
                time: new Date('2024-06-24T10:45:00'),
                note: 'Cliente cómoda, sin irritación',
                type: 'voice'
            }
        ],
        photos: [],
        totalCharged: 145.00,
        paymentMethod: 'card',
        clientRating: 5,
        clientFeedback: '¡Perfecto como siempre! Me encanta el resultado.',
        createdAt: new Date('2024-06-24T10:00:00')
    }
];
function getClientById(id) {
    return mockClients.find((client)=>client.id === id);
}
function getUserById(id) {
    return mockUsers.find((user)=>user.id === id);
}
function getProductById(id) {
    return mockProducts.find((product)=>product.id === id);
}
function getClientsByOrganization(orgId) {
    return mockClients.filter((client)=>client.organizationId === orgId);
}
function getProductsByOrganization(orgId) {
    return mockProducts.filter((product)=>product.organizationId === orgId);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/auth-context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth),
    "useRequireAuth": (()=>useRequireAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/mockData.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    _s();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [organization, setOrganization] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            // Simular carga inicial - verificar si hay usuario guardado
            const savedUser = localStorage.getItem('salonier-user');
            if (savedUser) {
                try {
                    const userData = JSON.parse(savedUser);
                    const foundUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockUsers"].find({
                        "AuthProvider.useEffect.foundUser": (u)=>u.id === userData.id
                    }["AuthProvider.useEffect.foundUser"]);
                    if (foundUser) {
                        setUser(foundUser);
                        const userOrg = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockOrganizations"].find({
                            "AuthProvider.useEffect.userOrg": (org)=>org.id === foundUser.organizationId
                        }["AuthProvider.useEffect.userOrg"]);
                        setOrganization(userOrg || null);
                    }
                } catch (error) {
                    console.error('Error loading saved user:', error);
                    localStorage.removeItem('salonier-user');
                }
            }
            setIsLoading(false);
        }
    }["AuthProvider.useEffect"], []);
    const login = async (email, password)=>{
        setIsLoading(true);
        // Simular delay de autenticación
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        // Buscar usuario en datos mock
        const foundUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockUsers"].find((u)=>u.email === email);
        if (foundUser && password === 'demo123') {
            setUser(foundUser);
            const userOrg = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockOrganizations"].find((org)=>org.id === foundUser.organizationId);
            setOrganization(userOrg || null);
            // Guardar en localStorage
            localStorage.setItem('salonier-user', JSON.stringify({
                id: foundUser.id
            }));
            setIsLoading(false);
            return true;
        }
        setIsLoading(false);
        return false;
    };
    const logout = ()=>{
        setUser(null);
        setOrganization(null);
        localStorage.removeItem('salonier-user');
    };
    const value = {
        user,
        organization,
        isLoading,
        login,
        logout,
        isAuthenticated: !!user
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/auth-context.tsx",
        lineNumber: 85,
        columnNumber: 5
    }, this);
}
_s(AuthProvider, "WlWG59Hesh6FskdJB9uS1NHnvEE=");
_c = AuthProvider;
function useAuth() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function useRequireAuth() {
    _s2();
    const { user, isLoading } = useAuth();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useRequireAuth.useEffect": ()=>{
            if (!isLoading && !user) {
                window.location.href = '/auth/login';
            }
        }
    }["useRequireAuth.useEffect"], [
        user,
        isLoading
    ]);
    return {
        user,
        isLoading
    };
}
_s2(useRequireAuth, "+eCjT2t6vEAs0iZAXnzW6JeO/lo=", false, function() {
    return [
        useAuth
    ];
});
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
            case REACT_ACTIVITY_TYPE:
                return "Activity";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_CONTEXT_TYPE:
                return (type.displayName || "Context") + ".Provider";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function UnknownOwner() {
        return Error("react-stack-top-frame");
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler");
    Symbol.for("react.provider");
    var REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_ACTIVITY_TYPE = Symbol.for("react.activity"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    };
    React = {
        "react-stack-bottom-frame": function(callStackForError) {
            return callStackForError();
        }
    };
    var specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var unknownOwnerDebugStack = React["react-stack-bottom-frame"].bind(React, UnknownOwner)();
    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error("react-stack-top-frame") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
}]);

//# sourceMappingURL=_f2e7029e._.js.map