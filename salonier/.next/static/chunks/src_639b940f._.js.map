{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount)\n}\n\nexport function formatPercentage(value: number): string {\n  return `${Math.round(value)}%`\n}\n\nexport function formatTime(minutes: number): string {\n  const hours = Math.floor(minutes / 60)\n  const mins = minutes % 60\n  \n  if (hours > 0) {\n    return `${hours}h ${mins}m`\n  }\n  return `${mins}m`\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function calculateAge(birthDate: Date): number {\n  const today = new Date()\n  const age = today.getFullYear() - birthDate.getFullYear()\n  const monthDiff = today.getMonth() - birthDate.getMonth()\n  \n  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\n    return age - 1\n  }\n  \n  return age\n}\n\nexport function timeAgo(date: Date): string {\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) return 'Hace un momento'\n  if (diffInSeconds < 3600) return `Hace ${Math.floor(diffInSeconds / 60)} min`\n  if (diffInSeconds < 86400) return `Hace ${Math.floor(diffInSeconds / 3600)} h`\n  if (diffInSeconds < 2592000) return `Hace ${Math.floor(diffInSeconds / 86400)} días`\n  if (diffInSeconds < 31536000) return `Hace ${Math.floor(diffInSeconds / 2592000)} meses`\n  return `Hace ${Math.floor(diffInSeconds / 31536000)} años`\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;AAChC;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,OAAO,UAAU;IAEvB,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;IAC7B;IACA,OAAO,GAAG,KAAK,CAAC,CAAC;AACnB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,aAAa,SAAe;IAC1C,MAAM,QAAQ,IAAI;IAClB,MAAM,MAAM,MAAM,WAAW,KAAK,UAAU,WAAW;IACvD,MAAM,YAAY,MAAM,QAAQ,KAAK,UAAU,QAAQ;IAEvD,IAAI,YAAY,KAAM,cAAc,KAAK,MAAM,OAAO,KAAK,UAAU,OAAO,IAAK;QAC/E,OAAO,MAAM;IACf;IAEA,OAAO;AACT;AAEO,SAAS,QAAQ,IAAU;IAChC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC;IAC7E,IAAI,gBAAgB,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,MAAM,EAAE,CAAC;IAC9E,IAAI,gBAAgB,SAAS,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC;IACpF,IAAI,gBAAgB,UAAU,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,SAAS,MAAM,CAAC;IACxF,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,UAAU,KAAK,CAAC;AAC5D;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center rounded-xl font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\"\n    \n    const variants = {\n      primary: \"bg-gradient-to-r from-primary to-secondary text-primary-foreground shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]\",\n      secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n      outline: \"border border-border bg-background hover:bg-muted hover:text-muted-foreground\",\n      ghost: \"hover:bg-muted hover:text-muted-foreground\",\n      destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\"\n    }\n    \n    const sizes = {\n      sm: \"h-9 px-3 text-sm\",\n      md: \"h-11 px-6 text-sm\",\n      lg: \"h-12 px-8 text-base\"\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { useAuth } from '@/lib/auth-context'\nimport { \n  Home, \n  Users, \n  Palette, \n  Package, \n  BarChart3, \n  Timer, \n  Settings,\n  LogOut,\n  Menu,\n  X\n} from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Clientes', href: '/clients', icon: Users },\n  { name: 'Formulación', href: '/formulas', icon: Palette },\n  { name: 'Inventario', href: '/inventory', icon: Package },\n  { name: 'Servicios', href: '/services', icon: Timer },\n  { name: 'Analytics', href: '/analytics', icon: BarChart3 },\n  { name: 'Configuración', href: '/settings', icon: Settings },\n]\n\nexport function Sidebar() {\n  const [isOpen, setIsOpen] = useState(false)\n  const pathname = usePathname()\n  const { user, logout } = useAuth()\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <div className=\"lg:hidden fixed top-4 left-4 z-50\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setIsOpen(!isOpen)}\n          className=\"bg-background/80 backdrop-blur-sm\"\n        >\n          {isOpen ? <X className=\"h-4 w-4\" /> : <Menu className=\"h-4 w-4\" />}\n        </Button>\n      </div>\n\n      {/* Overlay for mobile */}\n      {isOpen && (\n        <div \n          className=\"lg:hidden fixed inset-0 bg-black/50 z-40\"\n          onClick={() => setIsOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={cn(\n        \"fixed inset-y-0 left-0 z-50 w-64 bg-card border-r border-border transform transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:inset-0\",\n        isOpen ? \"translate-x-0\" : \"-translate-x-full\"\n      )}>\n        <div className=\"flex flex-col h-full\">\n          {/* Logo */}\n          <div className=\"flex items-center px-6 py-4 border-b border-border\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center\">\n              <span className=\"text-sm font-bold text-white\">S</span>\n            </div>\n            <span className=\"ml-3 text-xl font-bold gradient-text\">Salonier</span>\n          </div>\n\n          {/* User info */}\n          <div className=\"px-6 py-4 border-b border-border\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.name.charAt(0)}\n                </span>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-foreground truncate\">\n                  {user?.name}\n                </p>\n                <p className=\"text-xs text-muted-foreground truncate\">\n                  {user?.role === 'owner' ? 'Propietario' : \n                   user?.role === 'colorist' ? 'Colorista' : \n                   user?.role === 'admin' ? 'Administrador' : 'Asistente'}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsOpen(false)}\n                  className={cn(\n                    \"group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200\",\n                    isActive\n                      ? \"bg-primary text-primary-foreground shadow-md\"\n                      : \"text-muted-foreground hover:text-foreground hover:bg-muted\"\n                  )}\n                >\n                  <item.icon className={cn(\n                    \"mr-3 h-5 w-5 transition-colors\",\n                    isActive ? \"text-primary-foreground\" : \"text-muted-foreground group-hover:text-foreground\"\n                  )} />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Logout */}\n          <div className=\"px-4 py-4 border-t border-border\">\n            <Button\n              variant=\"ghost\"\n              className=\"w-full justify-start text-muted-foreground hover:text-foreground\"\n              onClick={logout}\n            >\n              <LogOut className=\"mr-3 h-5 w-5\" />\n              Cerrar Sesión\n            </Button>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAnBA;;;;;;;;AAqBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,sMAAA,CAAA,OAAI;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAY,MAAM,uMAAA,CAAA,QAAK;IAAC;IAClD;QAAE,MAAM;QAAe,MAAM;QAAa,MAAM,2MAAA,CAAA,UAAO;IAAC;IACxD;QAAE,MAAM;QAAc,MAAM;QAAc,MAAM,2MAAA,CAAA,UAAO;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAa,MAAM,uMAAA,CAAA,QAAK;IAAC;IACpD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,qNAAA,CAAA,YAAS;IAAC;IACzD;QAAE,MAAM;QAAiB,MAAM;QAAa,MAAM,6MAAA,CAAA,WAAQ;IAAC;CAC5D;AAEM,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAE/B,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,UAAU,CAAC;oBAC1B,WAAU;8BAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAAe,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;YAKzD,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;0BAK7B,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,iKACA,SAAS,kBAAkB;0BAE3B,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAIzD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDACb,MAAM,KAAK,OAAO;;;;;;;;;;;kDAGvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DACV,MAAM;;;;;;0DAET,6LAAC;gDAAE,WAAU;0DACV,MAAM,SAAS,UAAU,gBACzB,MAAM,SAAS,aAAa,cAC5B,MAAM,SAAS,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;sCAOpD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gGACA,WACI,iDACA;;sDAGN,6LAAC,KAAK,IAAI;4CAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACrB,kCACA,WAAW,4BAA4B;;;;;;wCAExC,KAAK,IAAI;;mCAdL,KAAK,IAAI;;;;;4BAiBpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;;kDAET,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GAvGgB;;QAEG,qIAAA,CAAA,cAAW;QACH,iIAAA,CAAA,UAAO;;;KAHlB", "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  icon?: React.ReactNode\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, icon, ...props }, ref) => {\n    return (\n      <div className=\"space-y-2\">\n        {label && (\n          <label className=\"text-sm font-medium text-foreground\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {icon && (\n            <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {icon}\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              \"flex h-11 w-full rounded-xl border border-border bg-input px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n              icon && \"pl-10\",\n              error && \"border-destructive focus-visible:ring-destructive\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n        </div>\n        {error && (\n          <p className=\"text-sm text-destructive\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAClD,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,sBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGL,6LAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4VACA,QAAQ,SACR,SAAS,qDACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;;;;;;;YAGZ,uBACC,6LAAC;gBAAE,WAAU;0BAA4B;;;;;;;;;;;;AAIjD;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Bell, Search, Sun, Moon } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { useAuth } from '@/lib/auth-context'\n\ninterface HeaderProps {\n  title: string\n  subtitle?: string\n}\n\nexport function Header({ title, subtitle }: HeaderProps) {\n  const { user, organization } = useAuth()\n\n  return (\n    <header className=\"bg-background/80 backdrop-blur-sm border-b border-border sticky top-0 z-30\">\n      <div className=\"px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Title Section */}\n          <div className=\"flex-1\">\n            <h1 className=\"text-2xl font-bold text-foreground\">{title}</h1>\n            {subtitle && (\n              <p className=\"text-sm text-muted-foreground mt-1\">{subtitle}</p>\n            )}\n          </div>\n\n          {/* Actions Section */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Search */}\n            <div className=\"hidden md:block\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  placeholder=\"Buscar clientes, servicios...\"\n                  className=\"pl-10 w-64\"\n                />\n              </div>\n            </div>\n\n            {/* Notifications */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n              <Bell className=\"h-5 w-5\" />\n              <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full text-xs flex items-center justify-center text-white\">\n                3\n              </span>\n            </Button>\n\n            {/* Theme Toggle */}\n            <Button variant=\"ghost\" size=\"sm\">\n              <Sun className=\"h-5 w-5\" />\n            </Button>\n\n            {/* User Menu */}\n            <div className=\"flex items-center space-x-3 pl-4 border-l border-border\">\n              <div className=\"hidden md:block text-right\">\n                <p className=\"text-sm font-medium text-foreground\">{user?.name}</p>\n                <p className=\"text-xs text-muted-foreground\">{organization?.name}</p>\n              </div>\n              <div className=\"w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.name.charAt(0)}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAe;;IACrD,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAErC,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;4BACnD,0BACC,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAMhB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;;kDAC1C,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAmH;;;;;;;;;;;;0CAMrI,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAC3B,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAuC,MAAM;;;;;;0DAC1D,6LAAC;gDAAE,WAAU;0DAAiC,cAAc;;;;;;;;;;;;kDAE9D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDACb,MAAM,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrC;GA1DgB;;QACiB,iIAAA,CAAA,UAAO;;;KADxB", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useRequireAuth } from '@/lib/auth-context'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\nimport { LoadingScreen } from '@/components/ui/loading'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  subtitle?: string\n}\n\nexport function DashboardLayout({ children, title, subtitle }: DashboardLayoutProps) {\n  const { user, isLoading } = useRequireAuth()\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center\">\n        <div className=\"text-center space-y-4\">\n          <div className=\"mx-auto w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-2xl flex items-center justify-center animate-pulse\">\n            <span className=\"text-2xl font-bold text-white\">S</span>\n          </div>\n          <div>\n            <h1 className=\"text-3xl font-bold gradient-text\"><PERSON><PERSON></h1>\n            <p className=\"text-muted-foreground mt-2\">Cargando...</p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // useRequireAuth will redirect to login\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Sidebar />\n      \n      <div className=\"lg:pl-64\">\n        <Header title={title} subtitle={subtitle} />\n        \n        <main className=\"p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAaO,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAwB;;IACjF,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEzC,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;kCAElD,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;IAKpD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,wCAAwC;;IACtD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;;;;;0BAER,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAEhC,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GApCgB;;QACc,iIAAA,CAAA,iBAAc;;;KAD5B", "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-border bg-card text-card-foreground shadow-soft\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4EACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 987, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/app/formulas/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { DashboardLayout } from '@/components/layout/dashboard-layout'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { \n  Plus, \n  Camera, \n  Palette, \n  Zap,\n  Search,\n  Filter,\n  Clock,\n  Star,\n  AlertTriangle,\n  Eye\n} from 'lucide-react'\nimport { mockClients } from '@/data/mockData'\nimport Link from 'next/link'\n\nexport default function FormulasPage() {\n  const [selectedTab, setSelectedTab] = useState('new')\n\n  return (\n    <DashboardLayout \n      title=\"Formulación de Color\" \n      subtitle=\"Análisis IA y generación de fórmulas precisas\"\n    >\n      <div className=\"space-y-6\">\n        {/* Tabs de navegación */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit\">\n          {[\n            { key: 'new', label: 'Nuevo Servicio', icon: Plus },\n            { key: 'correction', label: 'Corrección', icon: Zap },\n            { key: 'saved', label: 'Fórmulas Guardadas', icon: Palette }\n          ].map((tab) => (\n            <button\n              key={tab.key}\n              onClick={() => setSelectedTab(tab.key)}\n              className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                selectedTab === tab.key\n                  ? 'bg-white text-purple-600 shadow-sm'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              <tab.icon className=\"h-4 w-4 mr-2\" />\n              {tab.label}\n            </button>\n          ))}\n        </div>\n\n        {/* Contenido según tab seleccionado */}\n        {selectedTab === 'new' && <NewServiceFlow />}\n        {selectedTab === 'correction' && <CorrectionFlow />}\n        {selectedTab === 'saved' && <SavedFormulas />}\n      </div>\n    </DashboardLayout>\n  )\n}\n\nfunction NewServiceFlow() {\n  const [step, setStep] = useState(1)\n  const [selectedClient, setSelectedClient] = useState<string | null>(null)\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Indicador de progreso */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          {[\n            { step: 1, label: 'Cliente', icon: Search },\n            { step: 2, label: 'Análisis', icon: Camera },\n            { step: 3, label: 'Fórmula', icon: Palette },\n            { step: 4, label: 'Proceso', icon: Clock }\n          ].map((item) => (\n            <div key={item.step} className=\"flex items-center\">\n              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${\n                step >= item.step ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-600'\n              }`}>\n                <item.icon className=\"h-4 w-4\" />\n              </div>\n              <span className={`ml-2 text-sm font-medium ${\n                step >= item.step ? 'text-purple-600' : 'text-gray-500'\n              }`}>\n                {item.label}\n              </span>\n              {item.step < 4 && (\n                <div className={`w-8 h-0.5 ml-4 ${\n                  step > item.step ? 'bg-purple-600' : 'bg-gray-200'\n                }`} />\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Paso 1: Selección de cliente */}\n      {step === 1 && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Seleccionar Cliente</CardTitle>\n            <CardDescription>\n              Elige el cliente para el nuevo servicio de color\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <Input\n                  placeholder=\"Buscar cliente por nombre...\"\n                  className=\"pl-10\"\n                />\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto\">\n                {mockClients.slice(0, 6).map((client) => (\n                  <div\n                    key={client.id}\n                    onClick={() => setSelectedClient(client.id)}\n                    className={`p-4 border rounded-lg cursor-pointer transition-all ${\n                      selectedClient === client.id\n                        ? 'border-purple-500 bg-purple-50'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-400 rounded-full flex items-center justify-center\">\n                        <span className=\"text-sm font-semibold text-white\">\n                          {client.name.charAt(0)}\n                        </span>\n                      </div>\n                      <div className=\"flex-1\">\n                        <h3 className=\"font-medium\">{client.name}</h3>\n                        <div className=\"flex items-center gap-2 mt-1\">\n                          {client.vipStatus && (\n                            <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n                              <Star className=\"h-3 w-3 mr-1\" />\n                              VIP\n                            </span>\n                          )}\n                          {client.allergies.length > 0 && (\n                            <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                              <AlertTriangle className=\"h-3 w-3 mr-1\" />\n                              Alergias\n                            </span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              <div className=\"flex justify-between pt-4\">\n                <Link href=\"/clients\">\n                  <Button variant=\"outline\">\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    Nuevo Cliente\n                  </Button>\n                </Link>\n                <Button \n                  onClick={() => setStep(2)}\n                  disabled={!selectedClient}\n                >\n                  Continuar\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Paso 2: Análisis de color */}\n      {step === 2 && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Análisis de Color IA</CardTitle>\n            <CardDescription>\n              Captura fotos del cabello para análisis automático\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-6\">\n              {/* Zona de captura */}\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                {['Raíz', 'Medios', 'Puntas'].map((zone) => (\n                  <div key={zone} className=\"text-center\">\n                    <div className=\"w-full h-48 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50 hover:bg-gray-100 cursor-pointer transition-colors\">\n                      <div className=\"text-center\">\n                        <Camera className=\"h-8 w-8 text-gray-400 mx-auto mb-2\" />\n                        <p className=\"text-sm font-medium text-gray-600\">{zone}</p>\n                        <p className=\"text-xs text-gray-500\">Toca para capturar</p>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {/* Resultados del análisis (simulado) */}\n              <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                <h4 className=\"font-medium text-blue-900 mb-2\">Análisis IA Completado</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                  <div>\n                    <span className=\"font-medium\">Nivel base:</span> 5 (Castaño claro)\n                  </div>\n                  <div>\n                    <span className=\"font-medium\">Canas:</span> 25%\n                  </div>\n                  <div>\n                    <span className=\"font-medium\">Porosidad:</span> Media\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex justify-between\">\n                <Button variant=\"outline\" onClick={() => setStep(1)}>\n                  Anterior\n                </Button>\n                <Button onClick={() => setStep(3)}>\n                  Generar Fórmula\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Paso 3: Fórmula generada */}\n      {step === 3 && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Fórmula Generada</CardTitle>\n            <CardDescription>\n              Fórmula optimizada basada en el análisis IA\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-6\">\n              {/* Fórmula por zonas */}\n              <div className=\"space-y-4\">\n                {[\n                  { zone: 'Raíz', color: 'Wella 6/0', developer: '20 vol', time: '45 min', cost: '$12.50' },\n                  { zone: 'Medios', color: 'Wella 7/1', developer: '20 vol', time: '35 min', cost: '$10.25' },\n                  { zone: 'Puntas', color: 'Toner 9/16', developer: '6 vol', time: '20 min', cost: '$8.75' }\n                ].map((formula) => (\n                  <div key={formula.zone} className=\"p-4 border rounded-lg\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"font-medium\">{formula.zone}</h4>\n                      <span className=\"text-sm font-medium text-green-600\">{formula.cost}</span>\n                    </div>\n                    <div className=\"grid grid-cols-3 gap-4 text-sm text-gray-600\">\n                      <div>Color: {formula.color}</div>\n                      <div>Revelador: {formula.developer}</div>\n                      <div>Tiempo: {formula.time}</div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {/* Resumen de costos */}\n              <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                <div className=\"flex justify-between items-center\">\n                  <div>\n                    <h4 className=\"font-medium text-green-900\">Costo Total de Productos</h4>\n                    <p className=\"text-sm text-green-700\">Margen sugerido: 65%</p>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-2xl font-bold text-green-600\">$31.50</p>\n                    <p className=\"text-sm text-green-700\">Precio sugerido: $90</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"flex justify-between\">\n                <Button variant=\"outline\" onClick={() => setStep(2)}>\n                  Anterior\n                </Button>\n                <div className=\"space-x-2\">\n                  <Button variant=\"outline\">\n                    Guardar Fórmula\n                  </Button>\n                  <Button onClick={() => setStep(4)}>\n                    Iniciar Proceso\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Paso 4: Proceso en vivo */}\n      {step === 4 && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Proceso en Vivo</CardTitle>\n            <CardDescription>\n              Timers y documentación automática del servicio\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-center py-12\">\n              <Clock className=\"h-16 w-16 text-purple-600 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-semibold mb-2\">Proceso Iniciado</h3>\n              <p className=\"text-gray-600 mb-6\">\n                Los timers y la documentación automática estarán disponibles en la siguiente versión.\n              </p>\n              <Button onClick={() => setStep(1)}>\n                Nuevo Servicio\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n\nfunction CorrectionFlow() {\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center\">\n          <Zap className=\"h-5 w-5 mr-2 text-orange-500\" />\n          Modo Corrección Express\n        </CardTitle>\n        <CardDescription>\n          Análisis rápido y soluciones para correcciones de color\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"text-center py-12\">\n          <Zap className=\"h-16 w-16 text-orange-500 mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold mb-2\">Corrección Express</h3>\n          <p className=\"text-gray-600 mb-6\">\n            El modo de corrección express estará disponible en la próxima actualización.\n          </p>\n          <Button variant=\"outline\">\n            Próximamente\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\nfunction SavedFormulas() {\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center\">\n          <Palette className=\"h-5 w-5 mr-2\" />\n          Fórmulas Guardadas\n        </CardTitle>\n        <CardDescription>\n          Biblioteca de fórmulas exitosas y plantillas\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"text-center py-12\">\n          <Palette className=\"h-16 w-16 text-purple-600 mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold mb-2\">Sin fórmulas guardadas</h3>\n          <p className=\"text-gray-600 mb-6\">\n            Las fórmulas que guardes aparecerán aquí para reutilizar.\n          </p>\n          <Button variant=\"outline\">\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Crear Primera Fórmula\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;;;AApBA;;;;;;;;;AAsBe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC,sJAAA,CAAA,kBAAe;QACd,OAAM;QACN,UAAS;kBAET,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACZ;wBACC;4BAAE,KAAK;4BAAO,OAAO;4BAAkB,MAAM,qMAAA,CAAA,OAAI;wBAAC;wBAClD;4BAAE,KAAK;4BAAc,OAAO;4BAAc,MAAM,mMAAA,CAAA,MAAG;wBAAC;wBACpD;4BAAE,KAAK;4BAAS,OAAO;4BAAsB,MAAM,2MAAA,CAAA,UAAO;wBAAC;qBAC5D,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;4BAEC,SAAS,IAAM,eAAe,IAAI,GAAG;4BACrC,WAAW,CAAC,6EAA6E,EACvF,gBAAgB,IAAI,GAAG,GACnB,uCACA,qCACJ;;8CAEF,6LAAC,IAAI,IAAI;oCAAC,WAAU;;;;;;gCACnB,IAAI,KAAK;;2BATL,IAAI,GAAG;;;;;;;;;;gBAejB,gBAAgB,uBAAS,6LAAC;;;;;gBAC1B,gBAAgB,8BAAgB,6LAAC;;;;;gBACjC,gBAAgB,yBAAW,6LAAC;;;;;;;;;;;;;;;;AAIrC;GAtCwB;KAAA;AAwCxB,SAAS;;IACP,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ;wBACC;4BAAE,MAAM;4BAAG,OAAO;4BAAW,MAAM,yMAAA,CAAA,SAAM;wBAAC;wBAC1C;4BAAE,MAAM;4BAAG,OAAO;4BAAY,MAAM,yMAAA,CAAA,SAAM;wBAAC;wBAC3C;4BAAE,MAAM;4BAAG,OAAO;4BAAW,MAAM,2MAAA,CAAA,UAAO;wBAAC;wBAC3C;4BAAE,MAAM;4BAAG,OAAO;4BAAW,MAAM,uMAAA,CAAA,QAAK;wBAAC;qBAC1C,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;4BAAoB,WAAU;;8CAC7B,6LAAC;oCAAI,WAAW,CAAC,sDAAsD,EACrE,QAAQ,KAAK,IAAI,GAAG,6BAA6B,6BACjD;8CACA,cAAA,6LAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAEvB,6LAAC;oCAAK,WAAW,CAAC,yBAAyB,EACzC,QAAQ,KAAK,IAAI,GAAG,oBAAoB,iBACxC;8CACC,KAAK,KAAK;;;;;;gCAEZ,KAAK,IAAI,GAAG,mBACX,6LAAC;oCAAI,WAAW,CAAC,eAAe,EAC9B,OAAO,KAAK,IAAI,GAAG,kBAAkB,eACrC;;;;;;;2BAdI,KAAK,IAAI;;;;;;;;;;;;;;;YAsBxB,SAAS,mBACR,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;8CACZ,0HAAA,CAAA,cAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,uBAC5B,6LAAC;4CAEC,SAAS,IAAM,kBAAkB,OAAO,EAAE;4CAC1C,WAAW,CAAC,oDAAoD,EAC9D,mBAAmB,OAAO,EAAE,GACxB,mCACA,yCACJ;sDAEF,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEACb,OAAO,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;kEAGxB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAe,OAAO,IAAI;;;;;;0EACxC,6LAAC;gEAAI,WAAU;;oEACZ,OAAO,SAAS,kBACf,6LAAC;wEAAK,WAAU;;0FACd,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;oEAIpC,OAAO,SAAS,CAAC,MAAM,GAAG,mBACzB,6LAAC;wEAAK,WAAU;;0FACd,6LAAC,2NAAA,CAAA,gBAAa;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;2CAzB/C,OAAO,EAAE;;;;;;;;;;8CAoCpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;;kEACd,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAIrC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,QAAQ;4CACvB,UAAU,CAAC;sDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,SAAS,mBACR,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAQ;wCAAU;qCAAS,CAAC,GAAG,CAAC,CAAC,qBACjC,6LAAC;4CAAe,WAAU;sDACxB,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAE,WAAU;sEAAqC;;;;;;sEAClD,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;2CALjC;;;;;;;;;;8CAad,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAkB;;;;;;;8DAElD,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAa;;;;;;;8DAE7C,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;8CAKrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,QAAQ;sDAAI;;;;;;sDAGrD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,QAAQ;sDAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU5C,SAAS,mBACR,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,MAAM;4CAAQ,OAAO;4CAAa,WAAW;4CAAU,MAAM;4CAAU,MAAM;wCAAS;wCACxF;4CAAE,MAAM;4CAAU,OAAO;4CAAa,WAAW;4CAAU,MAAM;4CAAU,MAAM;wCAAS;wCAC1F;4CAAE,MAAM;4CAAU,OAAO;4CAAc,WAAW;4CAAS,MAAM;4CAAU,MAAM;wCAAQ;qCAC1F,CAAC,GAAG,CAAC,CAAC,wBACL,6LAAC;4CAAuB,WAAU;;8DAChC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAe,QAAQ,IAAI;;;;;;sEACzC,6LAAC;4DAAK,WAAU;sEAAsC,QAAQ,IAAI;;;;;;;;;;;;8DAEpE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAI;gEAAQ,QAAQ,KAAK;;;;;;;sEAC1B,6LAAC;;gEAAI;gEAAY,QAAQ,SAAS;;;;;;;sEAClC,6LAAC;;gEAAI;gEAAS,QAAQ,IAAI;;;;;;;;;;;;;;2CARpB,QAAQ,IAAI;;;;;;;;;;8CAe1B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAExC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;;;;;;8CAK5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,QAAQ;sDAAI;;;;;;sDAGrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;8DAAU;;;;;;8DAG1B,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAS,IAAM,QAAQ;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAW9C,SAAS,mBACR,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,QAAQ;8CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD;IAjQS;MAAA;AAmQT,SAAS;IACP,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAiC;;;;;;;kCAGlD,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;sCAAU;;;;;;;;;;;;;;;;;;;;;;;AAOpC;MA1BS;AA4BT,SAAS;IACP,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGtC,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;;8CACd,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;MA3BS", "debugId": null}}]}