{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/data/mockData.ts"], "sourcesContent": ["import { \n  User, \n  Organization, \n  Client, \n  Product, \n  Service, \n  Formula,\n  HairAnalysis,\n  BusinessMetrics \n} from '@/types'\n\n// Mock Organizations\nexport const mockOrganizations: Organization[] = [\n  {\n    id: 'org-1',\n    name: '<PERSON><PERSON>',\n    type: 'salon',\n    subscriptionTier: 'pro',\n    settings: {\n      currency: 'USD',\n      timezone: 'America/New_York',\n      businessHours: {\n        monday: { open: '09:00', close: '18:00', closed: false },\n        tuesday: { open: '09:00', close: '18:00', closed: false },\n        wednesday: { open: '09:00', close: '18:00', closed: false },\n        thursday: { open: '09:00', close: '20:00', closed: false },\n        friday: { open: '09:00', close: '20:00', closed: false },\n        saturday: { open: '08:00', close: '17:00', closed: false },\n        sunday: { open: '10:00', close: '16:00', closed: false },\n      },\n      defaultMargin: 65\n    },\n    createdAt: new Date('2024-01-15')\n  }\n]\n\n// Mock Users\nexport const mockUsers: User[] = [\n  {\n    id: 'user-1',\n    email: '<EMAIL>',\n    name: '<PERSON>',\n    role: 'owner',\n    organizationId: 'org-1',\n    avatar: '/avatars/maria.jpg',\n    preferences: {\n      theme: 'light',\n      language: 'es',\n      notifications: true,\n      defaultBrands: ['Wella', 'L\\'Oréal', 'Matrix']\n    },\n    createdAt: new Date('2024-01-15')\n  },\n  {\n    id: 'user-2',\n    email: '<EMAIL>',\n    name: 'Sofía Rodríguez',\n    role: 'colorist',\n    organizationId: 'org-1',\n    avatar: '/avatars/sofia.jpg',\n    preferences: {\n      theme: 'light',\n      language: 'es',\n      notifications: true,\n      defaultBrands: ['Wella', 'Schwarzkopf']\n    },\n    createdAt: new Date('2024-02-01')\n  }\n]\n\n// Mock Clients\nexport const mockClients: Client[] = [\n  {\n    id: 'client-1',\n    organizationId: 'org-1',\n    name: 'Laura García',\n    email: '<EMAIL>',\n    phone: '******-0123',\n    birthDate: new Date('1985-03-15'),\n    allergies: [\n      {\n        id: 'allergy-1',\n        name: 'PPD (Para-fenilendiamina)',\n        severity: 'high',\n        notes: 'Reacción severa en prueba anterior'\n      }\n    ],\n    preferences: {\n      preferredBrands: ['Wella', 'Matrix'],\n      avoidIngredients: ['PPD', 'Ammonia'],\n      maxProcessingTime: 60,\n      priceRange: { min: 80, max: 200 }\n    },\n    tags: ['VIP', 'Sensible'],\n    vipStatus: true,\n    photos: [],\n    notes: 'Cliente muy fiel, prefiere tonos fríos. Siempre puntual.',\n    createdAt: new Date('2024-01-20'),\n    lastVisit: new Date('2024-06-15')\n  },\n  {\n    id: 'client-2',\n    organizationId: 'org-1',\n    name: 'Carmen Ruiz',\n    email: '<EMAIL>',\n    phone: '******-0124',\n    birthDate: new Date('1978-08-22'),\n    allergies: [],\n    preferences: {\n      preferredBrands: ['L\\'Oréal'],\n      avoidIngredients: [],\n      maxProcessingTime: 90,\n      priceRange: { min: 100, max: 300 }\n    },\n    tags: ['Regular'],\n    vipStatus: false,\n    photos: [],\n    notes: 'Le gusta experimentar con colores nuevos.',\n    createdAt: new Date('2024-02-10'),\n    lastVisit: new Date('2024-06-10')\n  },\n  {\n    id: 'client-3',\n    organizationId: 'org-1',\n    name: 'Ana Martínez',\n    email: '<EMAIL>',\n    phone: '******-0125',\n    birthDate: new Date('1992-11-05'),\n    allergies: [],\n    preferences: {\n      preferredBrands: ['Matrix', 'Schwarzkopf'],\n      avoidIngredients: [],\n      maxProcessingTime: 75,\n      priceRange: { min: 60, max: 150 }\n    },\n    tags: ['Nueva'],\n    vipStatus: false,\n    photos: [],\n    notes: 'Primera visita muy exitosa, quiere mantener color natural.',\n    createdAt: new Date('2024-06-01'),\n    lastVisit: new Date('2024-06-20')\n  }\n]\n\n// Mock Products\nexport const mockProducts: Product[] = [\n  {\n    id: 'prod-1',\n    organizationId: 'org-1',\n    brand: 'Wella',\n    line: 'Koleston Perfect',\n    name: '6/0 Rubio Oscuro Natural',\n    code: 'WK-60',\n    type: 'color',\n    currentStock: 12,\n    unitCost: 8.50,\n    reorderPoint: 3,\n    properties: {\n      level: 6,\n      tone: 'natural',\n      size: 60,\n      unit: 'ml',\n      ingredients: ['Etanolamina', 'Resorcinol'],\n      ppd: false\n    },\n    createdAt: new Date('2024-01-15')\n  },\n  {\n    id: 'prod-2',\n    organizationId: 'org-1',\n    brand: 'Wella',\n    line: 'Welloxon Perfect',\n    name: 'Revelador 20 Vol',\n    code: 'WW-20',\n    type: 'developer',\n    currentStock: 8,\n    unitCost: 12.00,\n    reorderPoint: 2,\n    properties: {\n      volume: 20,\n      size: 1000,\n      unit: 'ml',\n      ingredients: ['Peróxido de Hidrógeno'],\n      ppd: false\n    },\n    createdAt: new Date('2024-01-15')\n  },\n  {\n    id: 'prod-3',\n    organizationId: 'org-1',\n    brand: 'Matrix',\n    line: 'SoColor',\n    name: '7A Rubio Medio Ceniza',\n    code: 'MS-7A',\n    type: 'color',\n    currentStock: 15,\n    unitCost: 9.25,\n    reorderPoint: 4,\n    properties: {\n      level: 7,\n      tone: 'ash',\n      size: 90,\n      unit: 'ml',\n      ingredients: ['MEA', 'Resorcinol'],\n      ppd: false\n    },\n    createdAt: new Date('2024-01-20')\n  }\n]\n\n// Mock Business Metrics\nexport const mockBusinessMetrics: BusinessMetrics = {\n  period: {\n    start: new Date('2024-06-01'),\n    end: new Date('2024-06-30')\n  },\n  revenue: 12450,\n  services: 89,\n  averageTicket: 139.89,\n  profitMargin: 68.5,\n  clientRetention: 85.2,\n  topServices: [\n    { name: 'Balayage Completo', count: 23, revenue: 4140, margin: 72.1 },\n    { name: 'Retoque de Raíz', count: 31, revenue: 2790, margin: 78.5 },\n    { name: 'Corrección de Color', count: 12, revenue: 2880, margin: 65.3 },\n    { name: 'Mechas Tradicionales', count: 18, revenue: 2340, margin: 69.8 },\n    { name: 'Tinte Completo', count: 5, revenue: 300, margin: 75.0 }\n  ],\n  topProducts: [\n    { name: 'Wella Koleston 6/0', usage: 450, cost: 38.25, efficiency: 92.3 },\n    { name: 'Matrix SoColor 7A', usage: 380, cost: 35.15, efficiency: 89.7 },\n    { name: 'Revelador 20 Vol', usage: 2100, cost: 25.20, efficiency: 95.1 },\n    { name: 'Olaplex No.1', usage: 125, cost: 62.50, efficiency: 88.4 }\n  ]\n}\n\n// Mock Recent Services\nexport const mockRecentServices: Service[] = [\n  {\n    id: 'service-1',\n    clientId: 'client-1',\n    formulaId: 'formula-1',\n    performedBy: 'user-2',\n    scheduledAt: new Date('2024-06-24T10:00:00'),\n    startedAt: new Date('2024-06-24T10:15:00'),\n    completedAt: new Date('2024-06-24T12:30:00'),\n    processNotes: [\n      {\n        time: new Date('2024-06-24T10:15:00'),\n        note: 'Iniciando aplicación en raíz',\n        type: 'system',\n        zone: 'roots'\n      },\n      {\n        time: new Date('2024-06-24T10:45:00'),\n        note: 'Cliente cómoda, sin irritación',\n        type: 'voice'\n      }\n    ],\n    photos: [],\n    totalCharged: 145.00,\n    paymentMethod: 'card',\n    clientRating: 5,\n    clientFeedback: '¡Perfecto como siempre! Me encanta el resultado.',\n    createdAt: new Date('2024-06-24T10:00:00')\n  }\n]\n\n// Helper functions for mock data\nexport function getClientById(id: string): Client | undefined {\n  return mockClients.find(client => client.id === id)\n}\n\nexport function getUserById(id: string): User | undefined {\n  return mockUsers.find(user => user.id === id)\n}\n\nexport function getProductById(id: string): Product | undefined {\n  return mockProducts.find(product => product.id === id)\n}\n\nexport function getClientsByOrganization(orgId: string): Client[] {\n  return mockClients.filter(client => client.organizationId === orgId)\n}\n\nexport function getProductsByOrganization(orgId: string): Product[] {\n  return mockProducts.filter(product => product.organizationId === orgId)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAYO,MAAM,oBAAoC;IAC/C;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,kBAAkB;QAClB,UAAU;YACR,UAAU;YACV,UAAU;YACV,eAAe;gBACb,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACvD,SAAS;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACxD,WAAW;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBAC1D,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACzD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACvD,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACzD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;YACzD;YACA,eAAe;QACjB;QACA,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,YAAoB;IAC/B;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,gBAAgB;QAChB,QAAQ;QACR,aAAa;YACX,OAAO;YACP,UAAU;YACV,eAAe;YACf,eAAe;gBAAC;gBAAS;gBAAY;aAAS;QAChD;QACA,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,gBAAgB;QAChB,QAAQ;QACR,aAAa;YACX,OAAO;YACP,UAAU;YACV,eAAe;YACf,eAAe;gBAAC;gBAAS;aAAc;QACzC;QACA,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,cAAwB;IACnC;QACE,IAAI;QACJ,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,OAAO;YACT;SACD;QACD,aAAa;YACX,iBAAiB;gBAAC;gBAAS;aAAS;YACpC,kBAAkB;gBAAC;gBAAO;aAAU;YACpC,mBAAmB;YACnB,YAAY;gBAAE,KAAK;gBAAI,KAAK;YAAI;QAClC;QACA,MAAM;YAAC;YAAO;SAAW;QACzB,WAAW;QACX,QAAQ,EAAE;QACV,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,EAAE;QACb,aAAa;YACX,iBAAiB;gBAAC;aAAW;YAC7B,kBAAkB,EAAE;YACpB,mBAAmB;YACnB,YAAY;gBAAE,KAAK;gBAAK,KAAK;YAAI;QACnC;QACA,MAAM;YAAC;SAAU;QACjB,WAAW;QACX,QAAQ,EAAE;QACV,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,EAAE;QACb,aAAa;YACX,iBAAiB;gBAAC;gBAAU;aAAc;YAC1C,kBAAkB,EAAE;YACpB,mBAAmB;YACnB,YAAY;gBAAE,KAAK;gBAAI,KAAK;YAAI;QAClC;QACA,MAAM;YAAC;SAAQ;QACf,WAAW;QACX,QAAQ,EAAE;QACV,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,gBAAgB;QAChB,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,cAAc;QACd,UAAU;QACV,cAAc;QACd,YAAY;YACV,OAAO;YACP,MAAM;YACN,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;gBAAe;aAAa;YAC1C,KAAK;QACP;QACA,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,gBAAgB;QAChB,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,cAAc;QACd,UAAU;QACV,cAAc;QACd,YAAY;YACV,QAAQ;YACR,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;aAAwB;YACtC,KAAK;QACP;QACA,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,gBAAgB;QAChB,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,cAAc;QACd,UAAU;QACV,cAAc;QACd,YAAY;YACV,OAAO;YACP,MAAM;YACN,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;gBAAO;aAAa;YAClC,KAAK;QACP;QACA,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,sBAAuC;IAClD,QAAQ;QACN,OAAO,IAAI,KAAK;QAChB,KAAK,IAAI,KAAK;IAChB;IACA,SAAS;IACT,UAAU;IACV,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,aAAa;QACX;YAAE,MAAM;YAAqB,OAAO;YAAI,SAAS;YAAM,QAAQ;QAAK;QACpE;YAAE,MAAM;YAAmB,OAAO;YAAI,SAAS;YAAM,QAAQ;QAAK;QAClE;YAAE,MAAM;YAAuB,OAAO;YAAI,SAAS;YAAM,QAAQ;QAAK;QACtE;YAAE,MAAM;YAAwB,OAAO;YAAI,SAAS;YAAM,QAAQ;QAAK;QACvE;YAAE,MAAM;YAAkB,OAAO;YAAG,SAAS;YAAK,QAAQ;QAAK;KAChE;IACD,aAAa;QACX;YAAE,MAAM;YAAsB,OAAO;YAAK,MAAM;YAAO,YAAY;QAAK;QACxE;YAAE,MAAM;YAAqB,OAAO;YAAK,MAAM;YAAO,YAAY;QAAK;QACvE;YAAE,MAAM;YAAoB,OAAO;YAAM,MAAM;YAAO,YAAY;QAAK;QACvE;YAAE,MAAM;YAAgB,OAAO;YAAK,MAAM;YAAO,YAAY;QAAK;KACnE;AACH;AAGO,MAAM,qBAAgC;IAC3C;QACE,IAAI;QACJ,UAAU;QACV,WAAW;QACX,aAAa;QACb,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;QACpB,aAAa,IAAI,KAAK;QACtB,cAAc;YACZ;gBACE,MAAM,IAAI,KAAK;gBACf,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM,IAAI,KAAK;gBACf,MAAM;gBACN,MAAM;YACR;SACD;QACD,QAAQ,EAAE;QACV,cAAc;QACd,eAAe;QACf,cAAc;QACd,gBAAgB;QAChB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,SAAS,cAAc,EAAU;IACtC,OAAO,YAAY,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AAClD;AAEO,SAAS,YAAY,EAAU;IACpC,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AAC5C;AAEO,SAAS,eAAe,EAAU;IACvC,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACrD;AAEO,SAAS,yBAAyB,KAAa;IACpD,OAAO,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,cAAc,KAAK;AAChE;AAEO,SAAS,0BAA0B,KAAa;IACrD,OAAO,aAAa,MAAM,CAAC,CAAA,UAAW,QAAQ,cAAc,KAAK;AACnE", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/lib/auth-context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect } from 'react'\nimport { User, Organization } from '@/types'\nimport { mockUsers, mockOrganizations } from '@/data/mockData'\nimport { LoadingScreen } from '@/components/ui/loading'\n\ninterface AuthContextType {\n  user: User | null\n  organization: Organization | null\n  isLoading: boolean\n  login: (email: string, password: string) => Promise<boolean>\n  logout: () => void\n  isAuthenticated: boolean\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [organization, setOrganization] = useState<Organization | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    // Simular carga inicial - verificar si hay usuario guardado\n    const savedUser = localStorage.getItem('salonier-user')\n    if (savedUser) {\n      try {\n        const userData = JSON.parse(savedUser)\n        const foundUser = mockUsers.find(u => u.id === userData.id)\n        if (foundUser) {\n          setUser(foundUser)\n          const userOrg = mockOrganizations.find(org => org.id === foundUser.organizationId)\n          setOrganization(userOrg || null)\n        }\n      } catch (error) {\n        console.error('Error loading saved user:', error)\n        localStorage.removeItem('salonier-user')\n      }\n    }\n    setIsLoading(false)\n  }, [])\n\n  const login = async (email: string, password: string): Promise<boolean> => {\n    setIsLoading(true)\n    \n    // Simular delay de autenticación\n    await new Promise(resolve => setTimeout(resolve, 1000))\n    \n    // Buscar usuario en datos mock\n    const foundUser = mockUsers.find(u => u.email === email)\n    \n    if (foundUser && password === 'demo123') { // Password mock para demo\n      setUser(foundUser)\n      const userOrg = mockOrganizations.find(org => org.id === foundUser.organizationId)\n      setOrganization(userOrg || null)\n      \n      // Guardar en localStorage\n      localStorage.setItem('salonier-user', JSON.stringify({ id: foundUser.id }))\n      \n      setIsLoading(false)\n      return true\n    }\n    \n    setIsLoading(false)\n    return false\n  }\n\n  const logout = () => {\n    setUser(null)\n    setOrganization(null)\n    localStorage.removeItem('salonier-user')\n  }\n\n  const value: AuthContextType = {\n    user,\n    organization,\n    isLoading,\n    login,\n    logout,\n    isAuthenticated: !!user\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\n// Hook para proteger rutas\nexport function useRequireAuth() {\n  const { user, isLoading } = useAuth()\n  \n  useEffect(() => {\n    if (!isLoading && !user) {\n      window.location.href = '/auth/login'\n    }\n  }, [user, isLoading])\n  \n  return { user, isLoading }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;;;AAJA;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,4DAA4D;YAC5D,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,IAAI,WAAW;gBACb,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC;oBAC5B,MAAM,YAAY,0HAAA,CAAA,YAAS,CAAC,IAAI;4DAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,EAAE;;oBAC1D,IAAI,WAAW;wBACb,QAAQ;wBACR,MAAM,UAAU,0HAAA,CAAA,oBAAiB,CAAC,IAAI;8DAAC,CAAA,MAAO,IAAI,EAAE,KAAK,UAAU,cAAc;;wBACjF,gBAAgB,WAAW;oBAC7B;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,6BAA6B;oBAC3C,aAAa,UAAU,CAAC;gBAC1B;YACF;YACA,aAAa;QACf;iCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,aAAa;QAEb,iCAAiC;QACjC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,+BAA+B;QAC/B,MAAM,YAAY,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QAElD,IAAI,aAAa,aAAa,WAAW;YACvC,QAAQ;YACR,MAAM,UAAU,0HAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,UAAU,cAAc;YACjF,gBAAgB,WAAW;YAE3B,0BAA0B;YAC1B,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;gBAAE,IAAI,UAAU,EAAE;YAAC;YAExE,aAAa;YACb,OAAO;QACT;QAEA,aAAa;QACb,OAAO;IACT;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,gBAAgB;QAChB,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAtEgB;KAAA;AAwET,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,aAAa,CAAC,MAAM;gBACvB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;mCAAG;QAAC;QAAM;KAAU;IAEpB,OAAO;QAAE;QAAM;IAAU;AAC3B;IAVgB;;QACc", "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount)\n}\n\nexport function formatPercentage(value: number): string {\n  return `${Math.round(value)}%`\n}\n\nexport function formatTime(minutes: number): string {\n  const hours = Math.floor(minutes / 60)\n  const mins = minutes % 60\n  \n  if (hours > 0) {\n    return `${hours}h ${mins}m`\n  }\n  return `${mins}m`\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function calculateAge(birthDate: Date): number {\n  const today = new Date()\n  const age = today.getFullYear() - birthDate.getFullYear()\n  const monthDiff = today.getMonth() - birthDate.getMonth()\n  \n  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\n    return age - 1\n  }\n  \n  return age\n}\n\nexport function timeAgo(date: Date): string {\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) return 'Hace un momento'\n  if (diffInSeconds < 3600) return `Hace ${Math.floor(diffInSeconds / 60)} min`\n  if (diffInSeconds < 86400) return `Hace ${Math.floor(diffInSeconds / 3600)} h`\n  if (diffInSeconds < 2592000) return `Hace ${Math.floor(diffInSeconds / 86400)} días`\n  if (diffInSeconds < 31536000) return `Hace ${Math.floor(diffInSeconds / 2592000)} meses`\n  return `Hace ${Math.floor(diffInSeconds / 31536000)} años`\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;AAChC;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,OAAO,UAAU;IAEvB,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;IAC7B;IACA,OAAO,GAAG,KAAK,CAAC,CAAC;AACnB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,aAAa,SAAe;IAC1C,MAAM,QAAQ,IAAI;IAClB,MAAM,MAAM,MAAM,WAAW,KAAK,UAAU,WAAW;IACvD,MAAM,YAAY,MAAM,QAAQ,KAAK,UAAU,QAAQ;IAEvD,IAAI,YAAY,KAAM,cAAc,KAAK,MAAM,OAAO,KAAK,UAAU,OAAO,IAAK;QAC/E,OAAO,MAAM;IACf;IAEA,OAAO;AACT;AAEO,SAAS,QAAQ,IAAU;IAChC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC;IAC7E,IAAI,gBAAgB,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,MAAM,EAAE,CAAC;IAC9E,IAAI,gBAAgB,SAAS,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC;IACpF,IAAI,gBAAgB,UAAU,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,SAAS,MAAM,CAAC;IACxF,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,UAAU,KAAK,CAAC;AAC5D;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/toast.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, createContext, useContext } from 'react'\nimport { cn } from '@/lib/utils'\nimport { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react'\n\ninterface Toast {\n  id: string\n  type: 'success' | 'error' | 'warning' | 'info'\n  title: string\n  description?: string\n  duration?: number\n}\n\ninterface ToastContextType {\n  toasts: Toast[]\n  addToast: (toast: Omit<Toast, 'id'>) => void\n  removeToast: (id: string) => void\n}\n\nconst ToastContext = createContext<ToastContextType | undefined>(undefined)\n\nexport function ToastProvider({ children }: { children: React.ReactNode }) {\n  const [toasts, setToasts] = useState<Toast[]>([])\n\n  const addToast = (toast: Omit<Toast, 'id'>) => {\n    const id = Math.random().toString(36).substr(2, 9)\n    const newToast = { ...toast, id }\n    setToasts(prev => [...prev, newToast])\n\n    // Auto remove after duration\n    const duration = toast.duration || 5000\n    setTimeout(() => {\n      removeToast(id)\n    }, duration)\n  }\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id))\n  }\n\n  return (\n    <ToastContext.Provider value={{ toasts, addToast, removeToast }}>\n      {children}\n      <ToastContainer />\n    </ToastContext.Provider>\n  )\n}\n\nexport function useToast() {\n  const context = useContext(ToastContext)\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider')\n  }\n  return context\n}\n\nfunction ToastContainer() {\n  const { toasts, removeToast } = useToast()\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full\">\n      {toasts.map((toast) => (\n        <ToastItem\n          key={toast.id}\n          toast={toast}\n          onRemove={() => removeToast(toast.id)}\n        />\n      ))}\n    </div>\n  )\n}\n\ninterface ToastItemProps {\n  toast: Toast\n  onRemove: () => void\n}\n\nfunction ToastItem({ toast, onRemove }: ToastItemProps) {\n  const [isVisible, setIsVisible] = useState(false)\n\n  useEffect(() => {\n    // Trigger animation\n    setTimeout(() => setIsVisible(true), 10)\n  }, [])\n\n  const handleRemove = () => {\n    setIsVisible(false)\n    setTimeout(onRemove, 200) // Wait for animation\n  }\n\n  const icons = {\n    success: CheckCircle,\n    error: AlertCircle,\n    warning: AlertTriangle,\n    info: Info\n  }\n\n  const styles = {\n    success: 'bg-green-50 border-green-200 text-green-800',\n    error: 'bg-red-50 border-red-200 text-red-800',\n    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n    info: 'bg-blue-50 border-blue-200 text-blue-800'\n  }\n\n  const iconStyles = {\n    success: 'text-green-500',\n    error: 'text-red-500',\n    warning: 'text-yellow-500',\n    info: 'text-blue-500'\n  }\n\n  const Icon = icons[toast.type]\n\n  return (\n    <div\n      className={cn(\n        'transform transition-all duration-200 ease-in-out',\n        isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0',\n        'bg-white border rounded-lg shadow-lg p-4',\n        styles[toast.type]\n      )}\n    >\n      <div className=\"flex items-start\">\n        <Icon className={cn('h-5 w-5 mt-0.5 mr-3 flex-shrink-0', iconStyles[toast.type])} />\n        <div className=\"flex-1 min-w-0\">\n          <h4 className=\"text-sm font-medium\">{toast.title}</h4>\n          {toast.description && (\n            <p className=\"text-sm opacity-90 mt-1\">{toast.description}</p>\n          )}\n        </div>\n        <button\n          onClick={handleRemove}\n          className=\"ml-3 flex-shrink-0 opacity-70 hover:opacity-100 transition-opacity\"\n        >\n          <X className=\"h-4 w-4\" />\n        </button>\n      </div>\n    </div>\n  )\n}\n\n// Helper functions for common toast types\nexport const toast = {\n  success: (title: string, description?: string) => {\n    const { addToast } = useToast()\n    addToast({ type: 'success', title, description })\n  },\n  error: (title: string, description?: string) => {\n    const { addToast } = useToast()\n    addToast({ type: 'error', title, description })\n  },\n  warning: (title: string, description?: string) => {\n    const { addToast } = useToast()\n    addToast({ type: 'warning', title, description })\n  },\n  info: (title: string, description?: string) => {\n    const { addToast } = useToast()\n    addToast({ type: 'info', title, description })\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAoBA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS,cAAc,EAAE,QAAQ,EAAiC;;IACvE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,WAAW,CAAC;QAChB,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE;QAAG;QAChC,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;QAErC,6BAA6B;QAC7B,MAAM,WAAW,MAAM,QAAQ,IAAI;QACnC,WAAW;YACT,YAAY;QACd,GAAG;IACL;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAQ;YAAU;QAAY;;YAC3D;0BACD,6LAAC;;;;;;;;;;;AAGP;GAzBgB;KAAA;AA2BT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAQhB,SAAS;;IACP,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG;IAEhC,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gBAEC,OAAO;gBACP,UAAU,IAAM,YAAY,MAAM,EAAE;eAF/B,MAAM,EAAE;;;;;;;;;;AAOvB;IAdS;;QACyB;;;MADzB;AAqBT,SAAS,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAkB;;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,oBAAoB;YACpB;uCAAW,IAAM,aAAa;sCAAO;QACvC;8BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,aAAa;QACb,WAAW,UAAU,KAAK,qBAAqB;;IACjD;IAEA,MAAM,QAAQ;QACZ,SAAS,8NAAA,CAAA,cAAW;QACpB,OAAO,uNAAA,CAAA,cAAW;QAClB,SAAS,2NAAA,CAAA,gBAAa;QACtB,MAAM,qMAAA,CAAA,OAAI;IACZ;IAEA,MAAM,SAAS;QACb,SAAS;QACT,OAAO;QACP,SAAS;QACT,MAAM;IACR;IAEA,MAAM,aAAa;QACjB,SAAS;QACT,OAAO;QACP,SAAS;QACT,MAAM;IACR;IAEA,MAAM,OAAO,KAAK,CAAC,MAAM,IAAI,CAAC;IAE9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA,YAAY,8BAA8B,8BAC1C,4CACA,MAAM,CAAC,MAAM,IAAI,CAAC;kBAGpB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC,UAAU,CAAC,MAAM,IAAI,CAAC;;;;;;8BAC/E,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuB,MAAM,KAAK;;;;;;wBAC/C,MAAM,WAAW,kBAChB,6LAAC;4BAAE,WAAU;sCAA2B,MAAM,WAAW;;;;;;;;;;;;8BAG7D,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKvB;IA9DS;MAAA;AAiEF,MAAM,QAAQ;IACnB,OAAO,MAAE,CAAC,OAAe;;QACvB,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,SAAS;YAAE,MAAM;YAAW;YAAO;QAAY;IACjD;;YAFuB;;;IAGvB,KAAK,MAAE,CAAC,OAAe;;QACrB,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,SAAS;YAAE,MAAM;YAAS;YAAO;QAAY;IAC/C;;YAFuB;;;IAGvB,OAAO,MAAE,CAAC,OAAe;;QACvB,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,SAAS;YAAE,MAAM;YAAW;YAAO;QAAY;IACjD;;YAFuB;;;IAGvB,IAAI,MAAE,CAAC,OAAe;;QACpB,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,SAAS;YAAE,MAAM;YAAQ;YAAO;QAAY;IAC9C;;YAFuB;;;AAGzB", "debugId": null}}]}