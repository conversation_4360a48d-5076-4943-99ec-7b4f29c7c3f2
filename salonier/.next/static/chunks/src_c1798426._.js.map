{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount)\n}\n\nexport function formatPercentage(value: number): string {\n  return `${Math.round(value)}%`\n}\n\nexport function formatTime(minutes: number): string {\n  const hours = Math.floor(minutes / 60)\n  const mins = minutes % 60\n  \n  if (hours > 0) {\n    return `${hours}h ${mins}m`\n  }\n  return `${mins}m`\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function calculateAge(birthDate: Date): number {\n  const today = new Date()\n  const age = today.getFullYear() - birthDate.getFullYear()\n  const monthDiff = today.getMonth() - birthDate.getMonth()\n  \n  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\n    return age - 1\n  }\n  \n  return age\n}\n\nexport function timeAgo(date: Date): string {\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) return 'Hace un momento'\n  if (diffInSeconds < 3600) return `Hace ${Math.floor(diffInSeconds / 60)} min`\n  if (diffInSeconds < 86400) return `Hace ${Math.floor(diffInSeconds / 3600)} h`\n  if (diffInSeconds < 2592000) return `Hace ${Math.floor(diffInSeconds / 86400)} días`\n  if (diffInSeconds < 31536000) return `Hace ${Math.floor(diffInSeconds / 2592000)} meses`\n  return `Hace ${Math.floor(diffInSeconds / 31536000)} años`\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;AAChC;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,OAAO,UAAU;IAEvB,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;IAC7B;IACA,OAAO,GAAG,KAAK,CAAC,CAAC;AACnB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,aAAa,SAAe;IAC1C,MAAM,QAAQ,IAAI;IAClB,MAAM,MAAM,MAAM,WAAW,KAAK,UAAU,WAAW;IACvD,MAAM,YAAY,MAAM,QAAQ,KAAK,UAAU,QAAQ;IAEvD,IAAI,YAAY,KAAM,cAAc,KAAK,MAAM,OAAO,KAAK,UAAU,OAAO,IAAK;QAC/E,OAAO,MAAM;IACf;IAEA,OAAO;AACT;AAEO,SAAS,QAAQ,IAAU;IAChC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC;IAC7E,IAAI,gBAAgB,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,MAAM,EAAE,CAAC;IAC9E,IAAI,gBAAgB,SAAS,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC;IACpF,IAAI,gBAAgB,UAAU,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,SAAS,MAAM,CAAC;IACxF,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,UAAU,KAAK,CAAC;AAC5D;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center rounded-xl font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\"\n    \n    const variants = {\n      primary: \"bg-gradient-to-r from-primary to-secondary text-primary-foreground shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]\",\n      secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n      outline: \"border border-border bg-background hover:bg-muted hover:text-muted-foreground\",\n      ghost: \"hover:bg-muted hover:text-muted-foreground\",\n      destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\"\n    }\n    \n    const sizes = {\n      sm: \"h-9 px-3 text-sm\",\n      md: \"h-11 px-6 text-sm\",\n      lg: \"h-12 px-8 text-base\"\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { useAuth } from '@/lib/auth-context'\nimport { \n  Home, \n  Users, \n  Palette, \n  Package, \n  BarChart3, \n  Timer, \n  Settings,\n  LogOut,\n  Menu,\n  X\n} from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Clientes', href: '/clients', icon: Users },\n  { name: 'Formulación', href: '/formulas', icon: Palette },\n  { name: 'Inventario', href: '/inventory', icon: Package },\n  { name: 'Servicios', href: '/services', icon: Timer },\n  { name: 'Analytics', href: '/analytics', icon: BarChart3 },\n  { name: 'Configuración', href: '/settings', icon: Settings },\n]\n\nexport function Sidebar() {\n  const [isOpen, setIsOpen] = useState(false)\n  const pathname = usePathname()\n  const { user, logout } = useAuth()\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <div className=\"lg:hidden fixed top-4 left-4 z-50\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setIsOpen(!isOpen)}\n          className=\"bg-background/80 backdrop-blur-sm\"\n        >\n          {isOpen ? <X className=\"h-4 w-4\" /> : <Menu className=\"h-4 w-4\" />}\n        </Button>\n      </div>\n\n      {/* Overlay for mobile */}\n      {isOpen && (\n        <div \n          className=\"lg:hidden fixed inset-0 bg-black/50 z-40\"\n          onClick={() => setIsOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={cn(\n        \"fixed inset-y-0 left-0 z-50 w-64 bg-card border-r border-border transform transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:inset-0\",\n        isOpen ? \"translate-x-0\" : \"-translate-x-full\"\n      )}>\n        <div className=\"flex flex-col h-full\">\n          {/* Logo */}\n          <div className=\"flex items-center px-6 py-4 border-b border-border\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center\">\n              <span className=\"text-sm font-bold text-white\">S</span>\n            </div>\n            <span className=\"ml-3 text-xl font-bold gradient-text\">Salonier</span>\n          </div>\n\n          {/* User info */}\n          <div className=\"px-6 py-4 border-b border-border\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.name.charAt(0)}\n                </span>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-foreground truncate\">\n                  {user?.name}\n                </p>\n                <p className=\"text-xs text-muted-foreground truncate\">\n                  {user?.role === 'owner' ? 'Propietario' : \n                   user?.role === 'colorist' ? 'Colorista' : \n                   user?.role === 'admin' ? 'Administrador' : 'Asistente'}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsOpen(false)}\n                  className={cn(\n                    \"group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200\",\n                    isActive\n                      ? \"bg-primary text-primary-foreground shadow-md\"\n                      : \"text-muted-foreground hover:text-foreground hover:bg-muted\"\n                  )}\n                >\n                  <item.icon className={cn(\n                    \"mr-3 h-5 w-5 transition-colors\",\n                    isActive ? \"text-primary-foreground\" : \"text-muted-foreground group-hover:text-foreground\"\n                  )} />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Logout */}\n          <div className=\"px-4 py-4 border-t border-border\">\n            <Button\n              variant=\"ghost\"\n              className=\"w-full justify-start text-muted-foreground hover:text-foreground\"\n              onClick={logout}\n            >\n              <LogOut className=\"mr-3 h-5 w-5\" />\n              Cerrar Sesión\n            </Button>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAnBA;;;;;;;;AAqBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,sMAAA,CAAA,OAAI;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAY,MAAM,uMAAA,CAAA,QAAK;IAAC;IAClD;QAAE,MAAM;QAAe,MAAM;QAAa,MAAM,2MAAA,CAAA,UAAO;IAAC;IACxD;QAAE,MAAM;QAAc,MAAM;QAAc,MAAM,2MAAA,CAAA,UAAO;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAa,MAAM,uMAAA,CAAA,QAAK;IAAC;IACpD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,qNAAA,CAAA,YAAS;IAAC;IACzD;QAAE,MAAM;QAAiB,MAAM;QAAa,MAAM,6MAAA,CAAA,WAAQ;IAAC;CAC5D;AAEM,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAE/B,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,UAAU,CAAC;oBAC1B,WAAU;8BAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAAe,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;YAKzD,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;0BAK7B,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,iKACA,SAAS,kBAAkB;0BAE3B,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAIzD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDACb,MAAM,KAAK,OAAO;;;;;;;;;;;kDAGvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DACV,MAAM;;;;;;0DAET,6LAAC;gDAAE,WAAU;0DACV,MAAM,SAAS,UAAU,gBACzB,MAAM,SAAS,aAAa,cAC5B,MAAM,SAAS,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;sCAOpD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gGACA,WACI,iDACA;;sDAGN,6LAAC,KAAK,IAAI;4CAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACrB,kCACA,WAAW,4BAA4B;;;;;;wCAExC,KAAK,IAAI;;mCAdL,KAAK,IAAI;;;;;4BAiBpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;;kDAET,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GAvGgB;;QAEG,qIAAA,CAAA,cAAW;QACH,iIAAA,CAAA,UAAO;;;KAHlB", "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  icon?: React.ReactNode\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, icon, ...props }, ref) => {\n    return (\n      <div className=\"space-y-2\">\n        {label && (\n          <label className=\"text-sm font-medium text-foreground\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {icon && (\n            <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {icon}\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              \"flex h-11 w-full rounded-xl border border-border bg-input px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n              icon && \"pl-10\",\n              error && \"border-destructive focus-visible:ring-destructive\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n        </div>\n        {error && (\n          <p className=\"text-sm text-destructive\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAClD,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,sBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGL,6LAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4VACA,QAAQ,SACR,SAAS,qDACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;;;;;;;YAGZ,uBACC,6LAAC;gBAAE,WAAU;0BAA4B;;;;;;;;;;;;AAIjD;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Bell, Search, Sun, Moon } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { useAuth } from '@/lib/auth-context'\n\ninterface HeaderProps {\n  title: string\n  subtitle?: string\n}\n\nexport function Header({ title, subtitle }: HeaderProps) {\n  const { user, organization } = useAuth()\n\n  return (\n    <header className=\"bg-background/80 backdrop-blur-sm border-b border-border sticky top-0 z-30\">\n      <div className=\"px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Title Section */}\n          <div className=\"flex-1\">\n            <h1 className=\"text-2xl font-bold text-foreground\">{title}</h1>\n            {subtitle && (\n              <p className=\"text-sm text-muted-foreground mt-1\">{subtitle}</p>\n            )}\n          </div>\n\n          {/* Actions Section */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Search */}\n            <div className=\"hidden md:block\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  placeholder=\"Buscar clientes, servicios...\"\n                  className=\"pl-10 w-64\"\n                />\n              </div>\n            </div>\n\n            {/* Notifications */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n              <Bell className=\"h-5 w-5\" />\n              <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full text-xs flex items-center justify-center text-white\">\n                3\n              </span>\n            </Button>\n\n            {/* Theme Toggle */}\n            <Button variant=\"ghost\" size=\"sm\">\n              <Sun className=\"h-5 w-5\" />\n            </Button>\n\n            {/* User Menu */}\n            <div className=\"flex items-center space-x-3 pl-4 border-l border-border\">\n              <div className=\"hidden md:block text-right\">\n                <p className=\"text-sm font-medium text-foreground\">{user?.name}</p>\n                <p className=\"text-xs text-muted-foreground\">{organization?.name}</p>\n              </div>\n              <div className=\"w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.name.charAt(0)}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAe;;IACrD,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAErC,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;4BACnD,0BACC,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAMhB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;;kDAC1C,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAmH;;;;;;;;;;;;0CAMrI,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAC3B,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAuC,MAAM;;;;;;0DAC1D,6LAAC;gDAAE,WAAU;0DAAiC,cAAc;;;;;;;;;;;;kDAE9D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDACb,MAAM,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrC;GA1DgB;;QACiB,iIAAA,CAAA,UAAO;;;KADxB", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useRequireAuth } from '@/lib/auth-context'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\nimport { LoadingScreen } from '@/components/ui/loading'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  subtitle?: string\n}\n\nexport function DashboardLayout({ children, title, subtitle }: DashboardLayoutProps) {\n  const { user, isLoading } = useRequireAuth()\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center\">\n        <div className=\"text-center space-y-4\">\n          <div className=\"mx-auto w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-2xl flex items-center justify-center animate-pulse\">\n            <span className=\"text-2xl font-bold text-white\">S</span>\n          </div>\n          <div>\n            <h1 className=\"text-3xl font-bold gradient-text\"><PERSON><PERSON></h1>\n            <p className=\"text-muted-foreground mt-2\">Cargando...</p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // useRequireAuth will redirect to login\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Sidebar />\n      \n      <div className=\"lg:pl-64\">\n        <Header title={title} subtitle={subtitle} />\n        \n        <main className=\"p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAaO,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAwB;;IACjF,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEzC,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;kCAElD,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;IAKpD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,wCAAwC;;IACtD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;;;;;0BAER,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAEhC,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GApCgB;;QACc,iIAAA,CAAA,iBAAc;;;KAD5B", "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-border bg-card text-card-foreground shadow-soft\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4EACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 987, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/app/services/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { DashboardLayout } from '@/components/layout/dashboard-layout'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { \n  Plus, \n  Clock, \n  Play,\n  Pause,\n  Square,\n  Timer,\n  Camera,\n  Mic,\n  Search,\n  Filter,\n  Calendar,\n  User,\n  Palette,\n  CheckCircle,\n  AlertCircle\n} from 'lucide-react'\nimport { mockRecentServices, mockClients, getUserById } from '@/data/mockData'\nimport { formatCurrency, timeAgo } from '@/lib/utils'\nimport Link from 'next/link'\n\nexport default function ServicesPage() {\n  const [selectedTab, setSelectedTab] = useState('active')\n  const [activeTimers, setActiveTimers] = useState<any[]>([])\n\n  return (\n    <DashboardLayout \n      title=\"Gestión de Servicios\" \n      subtitle=\"Control de servicios activos, timers y documentación\"\n    >\n      <div className=\"space-y-6\">\n        {/* Tabs de navegación */}\n        <div className=\"flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit\">\n          {[\n            { key: 'active', label: 'Servicios Activos', icon: Play },\n            { key: 'scheduled', label: 'Programados', icon: Calendar },\n            { key: 'completed', label: 'Completados', icon: CheckCircle },\n            { key: 'new', label: 'Nuevo Servicio', icon: Plus }\n          ].map((tab) => (\n            <button\n              key={tab.key}\n              onClick={() => setSelectedTab(tab.key)}\n              className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n                selectedTab === tab.key\n                  ? 'bg-white text-purple-600 shadow-sm'\n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              <tab.icon className=\"h-4 w-4 mr-2\" />\n              {tab.label}\n            </button>\n          ))}\n        </div>\n\n        {/* Contenido según tab seleccionado */}\n        {selectedTab === 'active' && <ActiveServices />}\n        {selectedTab === 'scheduled' && <ScheduledServices />}\n        {selectedTab === 'completed' && <CompletedServices />}\n        {selectedTab === 'new' && <NewServiceForm />}\n      </div>\n    </DashboardLayout>\n  )\n}\n\nfunction ActiveServices() {\n  const [timers, setTimers] = useState([\n    {\n      id: '1',\n      clientName: 'Laura García',\n      service: 'Balayage Completo',\n      zone: 'Raíz',\n      startTime: new Date(Date.now() - 25 * 60 * 1000), // 25 minutos atrás\n      duration: 45,\n      status: 'running'\n    },\n    {\n      id: '2',\n      clientName: 'Carmen Ruiz',\n      service: 'Retoque de Color',\n      zone: 'Medios',\n      startTime: new Date(Date.now() - 15 * 60 * 1000), // 15 minutos atrás\n      duration: 30,\n      status: 'running'\n    }\n  ])\n\n  const toggleTimer = (id: string) => {\n    setTimers(timers.map(timer => \n      timer.id === id \n        ? { ...timer, status: timer.status === 'running' ? 'paused' : 'running' }\n        : timer\n    ))\n  }\n\n  const stopTimer = (id: string) => {\n    setTimers(timers.filter(timer => timer.id !== id))\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {timers.length > 0 ? (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {timers.map((timer) => {\n            const elapsed = Math.floor((Date.now() - timer.startTime.getTime()) / 1000 / 60)\n            const remaining = timer.duration - elapsed\n            const progress = (elapsed / timer.duration) * 100\n\n            return (\n              <Card key={timer.id} className=\"border-l-4 border-l-purple-500\">\n                <CardHeader className=\"pb-3\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <CardTitle className=\"text-lg\">{timer.clientName}</CardTitle>\n                      <CardDescription>{timer.service} - {timer.zone}</CardDescription>\n                    </div>\n                    <div className={`w-3 h-3 rounded-full ${\n                      timer.status === 'running' ? 'bg-green-500 animate-pulse' : 'bg-yellow-500'\n                    }`} />\n                  </div>\n                </CardHeader>\n                \n                <CardContent className=\"space-y-4\">\n                  {/* Timer display */}\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-purple-600 mb-1\">\n                      {Math.floor(remaining / 60)}:{(remaining % 60).toString().padStart(2, '0')}\n                    </div>\n                    <p className=\"text-sm text-gray-600\">\n                      {remaining > 0 ? 'Tiempo restante' : 'Tiempo excedido'}\n                    </p>\n                  </div>\n\n                  {/* Progress bar */}\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between text-sm text-gray-600\">\n                      <span>Progreso</span>\n                      <span>{Math.min(progress, 100).toFixed(0)}%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div \n                        className={`h-2 rounded-full transition-all duration-1000 ${\n                          progress >= 100 ? 'bg-red-500' : 'bg-purple-500'\n                        }`}\n                        style={{ width: `${Math.min(progress, 100)}%` }}\n                      />\n                    </div>\n                  </div>\n\n                  {/* Controls */}\n                  <div className=\"flex gap-2\">\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => toggleTimer(timer.id)}\n                      className=\"flex-1\"\n                    >\n                      {timer.status === 'running' ? (\n                        <>\n                          <Pause className=\"h-4 w-4 mr-2\" />\n                          Pausar\n                        </>\n                      ) : (\n                        <>\n                          <Play className=\"h-4 w-4 mr-2\" />\n                          Reanudar\n                        </>\n                      )}\n                    </Button>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => stopTimer(timer.id)}\n                    >\n                      <Square className=\"h-4 w-4 mr-2\" />\n                      Finalizar\n                    </Button>\n                  </div>\n\n                  {/* Quick actions */}\n                  <div className=\"flex gap-2 pt-2 border-t\">\n                    <Button variant=\"ghost\" size=\"sm\" className=\"flex-1\">\n                      <Camera className=\"h-4 w-4 mr-2\" />\n                      Foto\n                    </Button>\n                    <Button variant=\"ghost\" size=\"sm\" className=\"flex-1\">\n                      <Mic className=\"h-4 w-4 mr-2\" />\n                      Nota\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            )\n          })}\n        </div>\n      ) : (\n        <Card>\n          <CardContent className=\"text-center py-12\">\n            <Timer className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-xl font-semibold mb-2\">No hay servicios activos</h3>\n            <p className=\"text-gray-600 mb-6\">\n              Inicia un nuevo servicio para comenzar a usar los timers inteligentes.\n            </p>\n            <Button>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Nuevo Servicio\n            </Button>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n\nfunction ScheduledServices() {\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center\">\n          <Calendar className=\"h-5 w-5 mr-2\" />\n          Servicios Programados\n        </CardTitle>\n        <CardDescription>\n          Citas y servicios agendados para los próximos días\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"text-center py-12\">\n          <Calendar className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold mb-2\">No hay servicios programados</h3>\n          <p className=\"text-gray-600 mb-6\">\n            Programa citas para organizar mejor tu agenda.\n          </p>\n          <Button variant=\"outline\">\n            <Plus className=\"h-4 w-4 mr-2\" />\n            Programar Cita\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\nfunction CompletedServices() {\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center\">\n          <CheckCircle className=\"h-5 w-5 mr-2\" />\n          Servicios Completados\n        </CardTitle>\n        <CardDescription>\n          Historial de servicios realizados recientemente\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"space-y-4\">\n          {mockRecentServices.map((service) => {\n            const client = mockClients.find(c => c.id === service.clientId)\n            const stylist = getUserById(service.performedBy)\n            \n            return (\n              <div key={service.id} className=\"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-400 rounded-full flex items-center justify-center\">\n                    <span className=\"text-sm font-semibold text-white\">\n                      {client?.name.charAt(0)}\n                    </span>\n                  </div>\n                  <div>\n                    <h3 className=\"font-medium text-gray-900\">{client?.name}</h3>\n                    <p className=\"text-sm text-gray-600\">\n                      Servicio de Color • {stylist?.name}\n                    </p>\n                    <p className=\"text-xs text-gray-500\">\n                      {timeAgo(service.scheduledAt)}\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"text-right\">\n                    <p className=\"font-semibold text-green-600\">\n                      {formatCurrency(service.totalCharged)}\n                    </p>\n                    {service.clientRating && (\n                      <div className=\"flex items-center\">\n                        <span className=\"text-sm text-yellow-500\">★</span>\n                        <span className=\"text-sm text-gray-600 ml-1\">\n                          {service.clientRating}/5\n                        </span>\n                      </div>\n                    )}\n                  </div>\n                  \n                  <Button variant=\"outline\" size=\"sm\">\n                    Ver Detalles\n                  </Button>\n                </div>\n              </div>\n            )\n          })}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\nfunction NewServiceForm() {\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center\">\n          <Plus className=\"h-5 w-5 mr-2\" />\n          Nuevo Servicio\n        </CardTitle>\n        <CardDescription>\n          Inicia un nuevo servicio con análisis IA y timers automáticos\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"text-center py-12\">\n          <Palette className=\"h-16 w-16 text-purple-600 mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold mb-2\">Crear Nuevo Servicio</h3>\n          <p className=\"text-gray-600 mb-6\">\n            Ve a la sección de Formulación para crear un servicio completo con análisis IA.\n          </p>\n          <Link href=\"/formulas\">\n            <Button>\n              <Palette className=\"h-4 w-4 mr-2\" />\n              Ir a Formulación\n            </Button>\n          </Link>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;;;AA1BA;;;;;;;;;AA4Be,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE1D,qBACE,6LAAC,sJAAA,CAAA,kBAAe;QACd,OAAM;QACN,UAAS;kBAET,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACZ;wBACC;4BAAE,KAAK;4BAAU,OAAO;4BAAqB,MAAM,qMAAA,CAAA,OAAI;wBAAC;wBACxD;4BAAE,KAAK;4BAAa,OAAO;4BAAe,MAAM,6MAAA,CAAA,WAAQ;wBAAC;wBACzD;4BAAE,KAAK;4BAAa,OAAO;4BAAe,MAAM,8NAAA,CAAA,cAAW;wBAAC;wBAC5D;4BAAE,KAAK;4BAAO,OAAO;4BAAkB,MAAM,qMAAA,CAAA,OAAI;wBAAC;qBACnD,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;4BAEC,SAAS,IAAM,eAAe,IAAI,GAAG;4BACrC,WAAW,CAAC,6EAA6E,EACvF,gBAAgB,IAAI,GAAG,GACnB,uCACA,qCACJ;;8CAEF,6LAAC,IAAI,IAAI;oCAAC,WAAU;;;;;;gCACnB,IAAI,KAAK;;2BATL,IAAI,GAAG;;;;;;;;;;gBAejB,gBAAgB,0BAAY,6LAAC;;;;;gBAC7B,gBAAgB,6BAAe,6LAAC;;;;;gBAChC,gBAAgB,6BAAe,6LAAC;;;;;gBAChC,gBAAgB,uBAAS,6LAAC;;;;;;;;;;;;;;;;AAInC;GAzCwB;KAAA;AA2CxB,SAAS;;IACP,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnC;YACE,IAAI;YACJ,YAAY;YACZ,SAAS;YACT,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK;YAC3C,UAAU;YACV,QAAQ;QACV;QACA;YACE,IAAI;YACJ,YAAY;YACZ,SAAS;YACT,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK;YAC3C,UAAU;YACV,QAAQ;QACV;KACD;IAED,MAAM,cAAc,CAAC;QACnB,UAAU,OAAO,GAAG,CAAC,CAAA,QACnB,MAAM,EAAE,KAAK,KACT;gBAAE,GAAG,KAAK;gBAAE,QAAQ,MAAM,MAAM,KAAK,YAAY,WAAW;YAAU,IACtE;IAER;IAEA,MAAM,YAAY,CAAC;QACjB,UAAU,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAChD;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,MAAM,GAAG,kBACf,6LAAC;YAAI,WAAU;sBACZ,OAAO,GAAG,CAAC,CAAC;gBACX,MAAM,UAAU,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,MAAM,SAAS,CAAC,OAAO,EAAE,IAAI,OAAO;gBAC7E,MAAM,YAAY,MAAM,QAAQ,GAAG;gBACnC,MAAM,WAAW,AAAC,UAAU,MAAM,QAAQ,GAAI;gBAE9C,qBACE,6LAAC,mIAAA,CAAA,OAAI;oBAAgB,WAAU;;sCAC7B,6LAAC,mIAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAW,MAAM,UAAU;;;;;;0DAChD,6LAAC,mIAAA,CAAA,kBAAe;;oDAAE,MAAM,OAAO;oDAAC;oDAAI,MAAM,IAAI;;;;;;;;;;;;;kDAEhD,6LAAC;wCAAI,WAAW,CAAC,qBAAqB,EACpC,MAAM,MAAM,KAAK,YAAY,+BAA+B,iBAC5D;;;;;;;;;;;;;;;;;sCAIN,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CAErB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,KAAK,CAAC,YAAY;gDAAI;gDAAE,CAAC,YAAY,EAAE,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;;sDAExE,6LAAC;4CAAE,WAAU;sDACV,YAAY,IAAI,oBAAoB;;;;;;;;;;;;8CAKzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;;wDAAM,KAAK,GAAG,CAAC,UAAU,KAAK,OAAO,CAAC;wDAAG;;;;;;;;;;;;;sDAE5C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAW,CAAC,8CAA8C,EACxD,YAAY,MAAM,eAAe,iBACjC;gDACF,OAAO;oDAAE,OAAO,GAAG,KAAK,GAAG,CAAC,UAAU,KAAK,CAAC,CAAC;gDAAC;;;;;;;;;;;;;;;;;8CAMpD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,YAAY,MAAM,EAAE;4CACnC,WAAU;sDAET,MAAM,MAAM,KAAK,0BAChB;;kEACE,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;6EAIpC;;kEACE,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;sDAKvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,UAAU,MAAM,EAAE;;8DAEjC,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAMvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;;8DAC1C,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGrC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;;8DAC1C,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;mBA7E7B,MAAM,EAAE;;;;;YAoFvB;;;;;iCAGF,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,6LAAC,qIAAA,CAAA,SAAM;;0CACL,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;IAnJS;MAAA;AAqJT,SAAS;IACP,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGvC,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;;8CACd,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;MA3BS;AA6BT,SAAS;IACP,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAG1C,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAI,WAAU;8BACZ,0HAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAC;wBACvB,MAAM,SAAS,0HAAA,CAAA,cAAW,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,QAAQ;wBAC9D,MAAM,UAAU,CAAA,GAAA,0HAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,WAAW;wBAE/C,qBACE,6LAAC;4BAAqB,WAAU;;8CAC9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,QAAQ,KAAK,OAAO;;;;;;;;;;;sDAGzB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6B,QAAQ;;;;;;8DACnD,6LAAC;oDAAE,WAAU;;wDAAwB;wDACd,SAAS;;;;;;;8DAEhC,6LAAC;oDAAE,WAAU;8DACV,CAAA,GAAA,sHAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,WAAW;;;;;;;;;;;;;;;;;;8CAKlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;;;;;;gDAErC,QAAQ,YAAY,kBACnB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA0B;;;;;;sEAC1C,6LAAC;4DAAK,WAAU;;gEACb,QAAQ,YAAY;gEAAC;;;;;;;;;;;;;;;;;;;sDAM9B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;2BAjC9B,QAAQ,EAAE;;;;;oBAuCxB;;;;;;;;;;;;;;;;;AAKV;MA/DS;AAiET,SAAS;IACP,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGnC,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;;kDACL,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD;MA7BS", "debugId": null}}]}