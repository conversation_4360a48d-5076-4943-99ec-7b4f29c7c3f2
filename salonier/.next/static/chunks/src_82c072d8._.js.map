{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount)\n}\n\nexport function formatPercentage(value: number): string {\n  return `${Math.round(value)}%`\n}\n\nexport function formatTime(minutes: number): string {\n  const hours = Math.floor(minutes / 60)\n  const mins = minutes % 60\n  \n  if (hours > 0) {\n    return `${hours}h ${mins}m`\n  }\n  return `${mins}m`\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function calculateAge(birthDate: Date): number {\n  const today = new Date()\n  const age = today.getFullYear() - birthDate.getFullYear()\n  const monthDiff = today.getMonth() - birthDate.getMonth()\n  \n  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\n    return age - 1\n  }\n  \n  return age\n}\n\nexport function timeAgo(date: Date): string {\n  const now = new Date()\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n  \n  if (diffInSeconds < 60) return 'Hace un momento'\n  if (diffInSeconds < 3600) return `Hace ${Math.floor(diffInSeconds / 60)} min`\n  if (diffInSeconds < 86400) return `Hace ${Math.floor(diffInSeconds / 3600)} h`\n  if (diffInSeconds < 2592000) return `Hace ${Math.floor(diffInSeconds / 86400)} días`\n  if (diffInSeconds < 31536000) return `Hace ${Math.floor(diffInSeconds / 2592000)} meses`\n  return `Hace ${Math.floor(diffInSeconds / 31536000)} años`\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa;IAC5C,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;AAChC;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,OAAO,UAAU;IAEvB,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;IAC7B;IACA,OAAO,GAAG,KAAK,CAAC,CAAC;AACnB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,aAAa,SAAe;IAC1C,MAAM,QAAQ,IAAI;IAClB,MAAM,MAAM,MAAM,WAAW,KAAK,UAAU,WAAW;IACvD,MAAM,YAAY,MAAM,QAAQ,KAAK,UAAU,QAAQ;IAEvD,IAAI,YAAY,KAAM,cAAc,KAAK,MAAM,OAAO,KAAK,UAAU,OAAO,IAAK;QAC/E,OAAO,MAAM;IACf;IAEA,OAAO;AACT;AAEO,SAAS,QAAQ,IAAU;IAChC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC;IAC7E,IAAI,gBAAgB,OAAO,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,MAAM,EAAE,CAAC;IAC9E,IAAI,gBAAgB,SAAS,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC;IACpF,IAAI,gBAAgB,UAAU,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,SAAS,MAAM,CAAC;IACxF,OAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,gBAAgB,UAAU,KAAK,CAAC;AAC5D;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center justify-center rounded-xl font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\"\n    \n    const variants = {\n      primary: \"bg-gradient-to-r from-primary to-secondary text-primary-foreground shadow-lg hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]\",\n      secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n      outline: \"border border-border bg-background hover:bg-muted hover:text-muted-foreground\",\n      ghost: \"hover:bg-muted hover:text-muted-foreground\",\n      destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\"\n    }\n    \n    const sizes = {\n      sm: \"h-9 px-3 text-sm\",\n      md: \"h-11 px-6 text-sm\",\n      lg: \"h-12 px-8 text-base\"\n    }\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { cn } from '@/lib/utils'\nimport { useAuth } from '@/lib/auth-context'\nimport { \n  Home, \n  Users, \n  Palette, \n  Package, \n  BarChart3, \n  Timer, \n  Settings,\n  LogOut,\n  Menu,\n  X\n} from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\nconst navigation = [\n  { name: 'Dashboard', href: '/dashboard', icon: Home },\n  { name: 'Clientes', href: '/clients', icon: Users },\n  { name: 'Formulación', href: '/formulas', icon: Palette },\n  { name: 'Inventario', href: '/inventory', icon: Package },\n  { name: 'Servicios', href: '/services', icon: Timer },\n  { name: 'Analytics', href: '/analytics', icon: BarChart3 },\n  { name: 'Configuración', href: '/settings', icon: Settings },\n]\n\nexport function Sidebar() {\n  const [isOpen, setIsOpen] = useState(false)\n  const pathname = usePathname()\n  const { user, logout } = useAuth()\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <div className=\"lg:hidden fixed top-4 left-4 z-50\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => setIsOpen(!isOpen)}\n          className=\"bg-background/80 backdrop-blur-sm\"\n        >\n          {isOpen ? <X className=\"h-4 w-4\" /> : <Menu className=\"h-4 w-4\" />}\n        </Button>\n      </div>\n\n      {/* Overlay for mobile */}\n      {isOpen && (\n        <div \n          className=\"lg:hidden fixed inset-0 bg-black/50 z-40\"\n          onClick={() => setIsOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={cn(\n        \"fixed inset-y-0 left-0 z-50 w-64 bg-card border-r border-border transform transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:inset-0\",\n        isOpen ? \"translate-x-0\" : \"-translate-x-full\"\n      )}>\n        <div className=\"flex flex-col h-full\">\n          {/* Logo */}\n          <div className=\"flex items-center px-6 py-4 border-b border-border\">\n            <div className=\"w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center\">\n              <span className=\"text-sm font-bold text-white\">S</span>\n            </div>\n            <span className=\"ml-3 text-xl font-bold gradient-text\">Salonier</span>\n          </div>\n\n          {/* User info */}\n          <div className=\"px-6 py-4 border-b border-border\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.name.charAt(0)}\n                </span>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-foreground truncate\">\n                  {user?.name}\n                </p>\n                <p className=\"text-xs text-muted-foreground truncate\">\n                  {user?.role === 'owner' ? 'Propietario' : \n                   user?.role === 'colorist' ? 'Colorista' : \n                   user?.role === 'admin' ? 'Administrador' : 'Asistente'}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 px-4 py-4 space-y-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsOpen(false)}\n                  className={cn(\n                    \"group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200\",\n                    isActive\n                      ? \"bg-primary text-primary-foreground shadow-md\"\n                      : \"text-muted-foreground hover:text-foreground hover:bg-muted\"\n                  )}\n                >\n                  <item.icon className={cn(\n                    \"mr-3 h-5 w-5 transition-colors\",\n                    isActive ? \"text-primary-foreground\" : \"text-muted-foreground group-hover:text-foreground\"\n                  )} />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Logout */}\n          <div className=\"px-4 py-4 border-t border-border\">\n            <Button\n              variant=\"ghost\"\n              className=\"w-full justify-start text-muted-foreground hover:text-foreground\"\n              onClick={logout}\n            >\n              <LogOut className=\"mr-3 h-5 w-5\" />\n              Cerrar Sesión\n            </Button>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAnBA;;;;;;;;AAqBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,sMAAA,CAAA,OAAI;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAY,MAAM,uMAAA,CAAA,QAAK;IAAC;IAClD;QAAE,MAAM;QAAe,MAAM;QAAa,MAAM,2MAAA,CAAA,UAAO;IAAC;IACxD;QAAE,MAAM;QAAc,MAAM;QAAc,MAAM,2MAAA,CAAA,UAAO;IAAC;IACxD;QAAE,MAAM;QAAa,MAAM;QAAa,MAAM,uMAAA,CAAA,QAAK;IAAC;IACpD;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,qNAAA,CAAA,YAAS;IAAC;IACzD;QAAE,MAAM;QAAiB,MAAM;QAAa,MAAM,6MAAA,CAAA,WAAQ;IAAC;CAC5D;AAEM,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAE/B,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,UAAU,CAAC;oBAC1B,WAAU;8BAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAAe,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;YAKzD,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,UAAU;;;;;;0BAK7B,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,iKACA,SAAS,kBAAkB;0BAE3B,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAIzD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDACb,MAAM,KAAK,OAAO;;;;;;;;;;;kDAGvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DACV,MAAM;;;;;;0DAET,6LAAC;gDAAE,WAAU;0DACV,MAAM,SAAS,UAAU,gBACzB,MAAM,SAAS,aAAa,cAC5B,MAAM,SAAS,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;sCAOpD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gGACA,WACI,iDACA;;sDAGN,6LAAC,KAAK,IAAI;4CAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACrB,kCACA,WAAW,4BAA4B;;;;;;wCAExC,KAAK,IAAI;;mCAdL,KAAK,IAAI;;;;;4BAiBpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;;kDAET,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GAvGgB;;QAEG,qIAAA,CAAA,cAAW;QACH,iIAAA,CAAA,UAAO;;;KAHlB", "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string\n  error?: string\n  icon?: React.ReactNode\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, icon, ...props }, ref) => {\n    return (\n      <div className=\"space-y-2\">\n        {label && (\n          <label className=\"text-sm font-medium text-foreground\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {icon && (\n            <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {icon}\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              \"flex h-11 w-full rounded-xl border border-border bg-input px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n              icon && \"pl-10\",\n              error && \"border-destructive focus-visible:ring-destructive\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n        </div>\n        {error && (\n          <p className=\"text-sm text-destructive\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAClD,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,sBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGL,6LAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4VACA,QAAQ,SACR,SAAS,qDACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;;;;;;;YAGZ,uBACC,6LAAC;gBAAE,WAAU;0BAA4B;;;;;;;;;;;;AAIjD;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Bell, Search, Sun, Moon } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { useAuth } from '@/lib/auth-context'\n\ninterface HeaderProps {\n  title: string\n  subtitle?: string\n}\n\nexport function Header({ title, subtitle }: HeaderProps) {\n  const { user, organization } = useAuth()\n\n  return (\n    <header className=\"bg-background/80 backdrop-blur-sm border-b border-border sticky top-0 z-30\">\n      <div className=\"px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Title Section */}\n          <div className=\"flex-1\">\n            <h1 className=\"text-2xl font-bold text-foreground\">{title}</h1>\n            {subtitle && (\n              <p className=\"text-sm text-muted-foreground mt-1\">{subtitle}</p>\n            )}\n          </div>\n\n          {/* Actions Section */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Search */}\n            <div className=\"hidden md:block\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  placeholder=\"Buscar clientes, servicios...\"\n                  className=\"pl-10 w-64\"\n                />\n              </div>\n            </div>\n\n            {/* Notifications */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n              <Bell className=\"h-5 w-5\" />\n              <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full text-xs flex items-center justify-center text-white\">\n                3\n              </span>\n            </Button>\n\n            {/* Theme Toggle */}\n            <Button variant=\"ghost\" size=\"sm\">\n              <Sun className=\"h-5 w-5\" />\n            </Button>\n\n            {/* User Menu */}\n            <div className=\"flex items-center space-x-3 pl-4 border-l border-border\">\n              <div className=\"hidden md:block text-right\">\n                <p className=\"text-sm font-medium text-foreground\">{user?.name}</p>\n                <p className=\"text-xs text-muted-foreground\">{organization?.name}</p>\n              </div>\n              <div className=\"w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center\">\n                <span className=\"text-sm font-medium text-white\">\n                  {user?.name.charAt(0)}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAe;;IACrD,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAErC,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;4BACnD,0BACC,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAMhB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;;kDAC1C,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAmH;;;;;;;;;;;;0CAMrI,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;0CAC3B,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAuC,MAAM;;;;;;0DAC1D,6LAAC;gDAAE,WAAU;0DAAiC,cAAc;;;;;;;;;;;;kDAE9D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDACb,MAAM,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrC;GA1DgB;;QACiB,iIAAA,CAAA,UAAO;;;KADxB", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useRequireAuth } from '@/lib/auth-context'\nimport { Sidebar } from './sidebar'\nimport { Header } from './header'\nimport { LoadingScreen } from '@/components/ui/loading'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  subtitle?: string\n}\n\nexport function DashboardLayout({ children, title, subtitle }: DashboardLayoutProps) {\n  const { user, isLoading } = useRequireAuth()\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center\">\n        <div className=\"text-center space-y-4\">\n          <div className=\"mx-auto w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-2xl flex items-center justify-center animate-pulse\">\n            <span className=\"text-2xl font-bold text-white\">S</span>\n          </div>\n          <div>\n            <h1 className=\"text-3xl font-bold gradient-text\"><PERSON><PERSON></h1>\n            <p className=\"text-muted-foreground mt-2\">Cargando...</p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // useRequireAuth will redirect to login\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Sidebar />\n      \n      <div className=\"lg:pl-64\">\n        <Header title={title} subtitle={subtitle} />\n        \n        <main className=\"p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAaO,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAwB;;IACjF,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEzC,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;kCAElD,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;IAKpD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,wCAAwC;;IACtD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;;;;;0BAER,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAO,UAAU;;;;;;kCAEhC,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GApCgB;;QACc,iIAAA,CAAA,iBAAc;;;KAD5B", "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-border bg-card text-card-foreground shadow-soft\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4EACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 987, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/app/analytics/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { DashboardLayout } from '@/components/layout/dashboard-layout'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { \n  TrendingUp, \n  TrendingDown,\n  DollarSign,\n  Users,\n  Palette,\n  Calendar,\n  Download,\n  Filter,\n  BarChart3,\n  PieChart,\n  Target\n} from 'lucide-react'\nimport { mockBusinessMetrics } from '@/data/mockData'\nimport { formatCurrency, formatPercentage } from '@/lib/utils'\n\nexport default function AnalyticsPage() {\n  const [selectedPeriod, setSelectedPeriod] = useState('month')\n  const [selectedView, setSelectedView] = useState('overview')\n\n  const metrics = mockBusinessMetrics\n\n  return (\n    <DashboardLayout \n      title=\"Analytics y Reportes\" \n      subtitle=\"Análisis de rendimiento y métricas de negocio\"\n    >\n      <div className=\"space-y-6\">\n        {/* Controles de período */}\n        <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n          <div className=\"flex gap-2\">\n            {[\n              { key: 'week', label: 'Esta Semana' },\n              { key: 'month', label: 'Este Mes' },\n              { key: 'quarter', label: 'Trimestre' },\n              { key: 'year', label: 'Año' }\n            ].map((period) => (\n              <Button\n                key={period.key}\n                variant={selectedPeriod === period.key ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedPeriod(period.key)}\n              >\n                {period.label}\n              </Button>\n            ))}\n          </div>\n          \n          <div className=\"flex gap-2\">\n            <Button variant=\"outline\" size=\"sm\">\n              <Filter className=\"h-4 w-4 mr-2\" />\n              Filtros\n            </Button>\n            <Button variant=\"outline\" size=\"sm\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Exportar\n            </Button>\n          </div>\n        </div>\n\n        {/* Métricas principales */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Ingresos</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {formatCurrency(metrics.revenue)}\n                  </p>\n                  <div className=\"flex items-center mt-1\">\n                    <TrendingUp className=\"h-4 w-4 text-green-500 mr-1\" />\n                    <span className=\"text-sm text-green-600 font-medium\">+12.5%</span>\n                    <span className=\"text-sm text-gray-500 ml-1\">vs mes anterior</span>\n                  </div>\n                </div>\n                <DollarSign className=\"h-8 w-8 text-green-600\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Servicios</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{metrics.services}</p>\n                  <div className=\"flex items-center mt-1\">\n                    <TrendingUp className=\"h-4 w-4 text-green-500 mr-1\" />\n                    <span className=\"text-sm text-green-600 font-medium\">+8.2%</span>\n                    <span className=\"text-sm text-gray-500 ml-1\">vs mes anterior</span>\n                  </div>\n                </div>\n                <Palette className=\"h-8 w-8 text-purple-600\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Ticket Promedio</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {formatCurrency(metrics.averageTicket)}\n                  </p>\n                  <div className=\"flex items-center mt-1\">\n                    <TrendingUp className=\"h-4 w-4 text-green-500 mr-1\" />\n                    <span className=\"text-sm text-green-600 font-medium\">+5.1%</span>\n                    <span className=\"text-sm text-gray-500 ml-1\">vs mes anterior</span>\n                  </div>\n                </div>\n                <Target className=\"h-8 w-8 text-blue-600\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Margen de Ganancia</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {formatPercentage(metrics.profitMargin)}\n                  </p>\n                  <div className=\"flex items-center mt-1\">\n                    <TrendingUp className=\"h-4 w-4 text-green-500 mr-1\" />\n                    <span className=\"text-sm text-green-600 font-medium\">+2.3%</span>\n                    <span className=\"text-sm text-gray-500 ml-1\">vs mes anterior</span>\n                  </div>\n                </div>\n                <BarChart3 className=\"h-8 w-8 text-orange-600\" />\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Servicios más populares */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <Palette className=\"h-5 w-5 mr-2\" />\n                Servicios Más Populares\n              </CardTitle>\n              <CardDescription>\n                Ranking de servicios por ingresos y frecuencia\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {metrics.topServices.map((service, index) => (\n                  <div key={service.name} className=\"flex items-center justify-between p-3 rounded-lg bg-gray-50\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\">\n                        <span className=\"text-sm font-bold text-purple-600\">#{index + 1}</span>\n                      </div>\n                      <div>\n                        <p className=\"font-medium text-gray-900\">{service.name}</p>\n                        <p className=\"text-sm text-gray-600\">{service.count} servicios</p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"font-semibold text-green-600\">\n                        {formatCurrency(service.revenue)}\n                      </p>\n                      <p className=\"text-sm text-gray-600\">\n                        {formatPercentage(service.margin)} margen\n                      </p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Productos más utilizados */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center\">\n                <BarChart3 className=\"h-5 w-5 mr-2\" />\n                Productos Más Utilizados\n              </CardTitle>\n              <CardDescription>\n                Análisis de consumo y eficiencia de productos\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {metrics.topProducts.map((product, index) => (\n                  <div key={product.name} className=\"flex items-center justify-between p-3 rounded-lg bg-gray-50\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                        <span className=\"text-sm font-bold text-blue-600\">#{index + 1}</span>\n                      </div>\n                      <div>\n                        <p className=\"font-medium text-gray-900\">{product.name}</p>\n                        <p className=\"text-sm text-gray-600\">{product.usage}ml utilizados</p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"font-semibold text-gray-900\">\n                        {formatCurrency(product.cost)}\n                      </p>\n                      <p className=\"text-sm text-green-600\">\n                        {formatPercentage(product.efficiency)} eficiencia\n                      </p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Gráficos de tendencias */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Tendencia de Ingresos</CardTitle>\n              <CardDescription>\n                Evolución de ingresos en los últimos 6 meses\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"h-64 flex items-center justify-center bg-gray-50 rounded-lg\">\n                <div className=\"text-center\">\n                  <BarChart3 className=\"h-12 w-12 text-gray-400 mx-auto mb-2\" />\n                  <p className=\"text-gray-600\">Gráfico de tendencias</p>\n                  <p className=\"text-sm text-gray-500\">Disponible en la próxima versión</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle>Distribución de Servicios</CardTitle>\n              <CardDescription>\n                Proporción de tipos de servicios realizados\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"h-64 flex items-center justify-center bg-gray-50 rounded-lg\">\n                <div className=\"text-center\">\n                  <PieChart className=\"h-12 w-12 text-gray-400 mx-auto mb-2\" />\n                  <p className=\"text-gray-600\">Gráfico circular</p>\n                  <p className=\"text-sm text-gray-500\">Disponible en la próxima versión</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Métricas de retención */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <Users className=\"h-5 w-5 mr-2\" />\n              Análisis de Clientes\n            </CardTitle>\n            <CardDescription>\n              Métricas de retención y satisfacción de clientes\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div className=\"text-center p-6 bg-green-50 rounded-lg\">\n                <div className=\"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3\">\n                  <Users className=\"h-6 w-6 text-green-600\" />\n                </div>\n                <p className=\"text-2xl font-bold text-green-600\">\n                  {formatPercentage(metrics.clientRetention)}\n                </p>\n                <p className=\"text-sm text-green-700 font-medium\">Retención de Clientes</p>\n                <p className=\"text-xs text-green-600 mt-1\">+2.3% vs mes anterior</p>\n              </div>\n\n              <div className=\"text-center p-6 bg-blue-50 rounded-lg\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3\">\n                  <Calendar className=\"h-6 w-6 text-blue-600\" />\n                </div>\n                <p className=\"text-2xl font-bold text-blue-600\">28</p>\n                <p className=\"text-sm text-blue-700 font-medium\">Días Promedio Entre Visitas</p>\n                <p className=\"text-xs text-blue-600 mt-1\">-3 días vs mes anterior</p>\n              </div>\n\n              <div className=\"text-center p-6 bg-yellow-50 rounded-lg\">\n                <div className=\"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3\">\n                  <Target className=\"h-6 w-6 text-yellow-600\" />\n                </div>\n                <p className=\"text-2xl font-bold text-yellow-600\">4.8</p>\n                <p className=\"text-sm text-yellow-700 font-medium\">Satisfacción Promedio</p>\n                <p className=\"text-xs text-yellow-600 mt-1\">+0.2 vs mes anterior</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Objetivos y metas */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <Target className=\"h-5 w-5 mr-2\" />\n              Objetivos del Mes\n            </CardTitle>\n            <CardDescription>\n              Progreso hacia las metas establecidas\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {[\n                { goal: 'Ingresos Mensuales', target: 15000, current: 12450, unit: '$' },\n                { goal: 'Servicios Realizados', target: 100, current: 89, unit: '' },\n                { goal: 'Nuevos Clientes', target: 15, current: 12, unit: '' },\n                { goal: 'Retención de Clientes', target: 90, current: 85.2, unit: '%' }\n              ].map((objective) => {\n                const progress = (objective.current / objective.target) * 100\n                return (\n                  <div key={objective.goal} className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"font-medium text-gray-900\">{objective.goal}</span>\n                      <span className=\"text-sm text-gray-600\">\n                        {objective.unit === '$' ? formatCurrency(objective.current) : `${objective.current}${objective.unit}`} / \n                        {objective.unit === '$' ? formatCurrency(objective.target) : `${objective.target}${objective.unit}`}\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div \n                        className={`h-2 rounded-full ${\n                          progress >= 100 ? 'bg-green-500' :\n                          progress >= 75 ? 'bg-blue-500' :\n                          progress >= 50 ? 'bg-yellow-500' : 'bg-red-500'\n                        }`}\n                        style={{ width: `${Math.min(progress, 100)}%` }}\n                      />\n                    </div>\n                    <div className=\"text-right\">\n                      <span className={`text-sm font-medium ${\n                        progress >= 100 ? 'text-green-600' :\n                        progress >= 75 ? 'text-blue-600' :\n                        progress >= 50 ? 'text-yellow-600' : 'text-red-600'\n                      }`}>\n                        {progress.toFixed(1)}% completado\n                      </span>\n                    </div>\n                  </div>\n                )\n              })}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;;;AApBA;;;;;;;;AAsBe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,UAAU,0HAAA,CAAA,sBAAmB;IAEnC,qBACE,6LAAC,sJAAA,CAAA,kBAAe;QACd,OAAM;QACN,UAAS;kBAET,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,KAAK;oCAAQ,OAAO;gCAAc;gCACpC;oCAAE,KAAK;oCAAS,OAAO;gCAAW;gCAClC;oCAAE,KAAK;oCAAW,OAAO;gCAAY;gCACrC;oCAAE,KAAK;oCAAQ,OAAO;gCAAM;6BAC7B,CAAC,GAAG,CAAC,CAAC,uBACL,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAS,mBAAmB,OAAO,GAAG,GAAG,YAAY;oCACrD,MAAK;oCACL,SAAS,IAAM,kBAAkB,OAAO,GAAG;8CAE1C,OAAO,KAAK;mCALR,OAAO,GAAG;;;;;;;;;;sCAUrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;8BAO3C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,OAAO;;;;;;8DAEjC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAK,WAAU;sEAAqC;;;;;;sEACrD,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAGjD,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAK5B,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAAoC,QAAQ,QAAQ;;;;;;8DACjE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAK,WAAU;sEAAqC;;;;;;sEACrD,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAGjD,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKzB,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa;;;;;;8DAEvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAK,WAAU;sEAAqC;;;;;;sEACrD,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAGjD,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKxB,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DACV,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,YAAY;;;;;;8DAExC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAK,WAAU;sEAAqC;;;;;;sEACrD,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAGjD,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAM7B,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGtC,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,SAAS,sBACjC,6LAAC;gDAAuB,WAAU;;kEAChC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;;wEAAoC;wEAAE,QAAQ;;;;;;;;;;;;0EAEhE,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA6B,QAAQ,IAAI;;;;;;kFACtD,6LAAC;wEAAE,WAAU;;4EAAyB,QAAQ,KAAK;4EAAC;;;;;;;;;;;;;;;;;;;kEAGxD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,OAAO;;;;;;0EAEjC,6LAAC;gEAAE,WAAU;;oEACV,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,MAAM;oEAAE;;;;;;;;;;;;;;+CAf9B,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;sCAyB9B,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,qNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGxC,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,SAAS,sBACjC,6LAAC;gDAAuB,WAAU;;kEAChC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;;wEAAkC;wEAAE,QAAQ;;;;;;;;;;;;0EAE9D,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA6B,QAAQ,IAAI;;;;;;kFACtD,6LAAC;wEAAE,WAAU;;4EAAyB,QAAQ,KAAK;4EAAC;;;;;;;;;;;;;;;;;;;kEAGxD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI;;;;;;0EAE9B,6LAAC;gEAAE,WAAU;;oEACV,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,UAAU;oEAAE;;;;;;;;;;;;;;+CAflC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BA0BhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM7C,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ/C,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGpC,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,6LAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,eAAe;;;;;;0DAE3C,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAClD,6LAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;kDAG7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;0DAChD,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAG5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAClD,6LAAC;gDAAE,WAAU;0DAAsC;;;;;;0DACnD,6LAAC;gDAAE,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOpD,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAsB,QAAQ;wCAAO,SAAS;wCAAO,MAAM;oCAAI;oCACvE;wCAAE,MAAM;wCAAwB,QAAQ;wCAAK,SAAS;wCAAI,MAAM;oCAAG;oCACnE;wCAAE,MAAM;wCAAmB,QAAQ;wCAAI,SAAS;wCAAI,MAAM;oCAAG;oCAC7D;wCAAE,MAAM;wCAAyB,QAAQ;wCAAI,SAAS;wCAAM,MAAM;oCAAI;iCACvE,CAAC,GAAG,CAAC,CAAC;oCACL,MAAM,WAAW,AAAC,UAAU,OAAO,GAAG,UAAU,MAAM,GAAI;oCAC1D,qBACE,6LAAC;wCAAyB,WAAU;;0DAClC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA6B,UAAU,IAAI;;;;;;kEAC3D,6LAAC;wDAAK,WAAU;;4DACb,UAAU,IAAI,KAAK,MAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,OAAO,IAAI,GAAG,UAAU,OAAO,GAAG,UAAU,IAAI,EAAE;4DAAC;4DACrG,UAAU,IAAI,KAAK,MAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,MAAM,IAAI,GAAG,UAAU,MAAM,GAAG,UAAU,IAAI,EAAE;;;;;;;;;;;;;0DAGvG,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAW,CAAC,iBAAiB,EAC3B,YAAY,MAAM,iBAClB,YAAY,KAAK,gBACjB,YAAY,KAAK,kBAAkB,cACnC;oDACF,OAAO;wDAAE,OAAO,GAAG,KAAK,GAAG,CAAC,UAAU,KAAK,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAGlD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAW,CAAC,oBAAoB,EACpC,YAAY,MAAM,mBAClB,YAAY,KAAK,kBACjB,YAAY,KAAK,oBAAoB,gBACrC;;wDACC,SAAS,OAAO,CAAC;wDAAG;;;;;;;;;;;;;uCAxBjB,UAAU,IAAI;;;;;gCA6B5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd;GApVwB;KAAA", "debugId": null}}]}