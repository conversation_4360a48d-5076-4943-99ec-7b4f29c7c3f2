{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/data/mockData.ts"], "sourcesContent": ["import { \n  User, \n  Organization, \n  Client, \n  Product, \n  Service, \n  Formula,\n  HairAnalysis,\n  BusinessMetrics \n} from '@/types'\n\n// Mock Organizations\nexport const mockOrganizations: Organization[] = [\n  {\n    id: 'org-1',\n    name: '<PERSON><PERSON>',\n    type: 'salon',\n    subscriptionTier: 'pro',\n    settings: {\n      currency: 'USD',\n      timezone: 'America/New_York',\n      businessHours: {\n        monday: { open: '09:00', close: '18:00', closed: false },\n        tuesday: { open: '09:00', close: '18:00', closed: false },\n        wednesday: { open: '09:00', close: '18:00', closed: false },\n        thursday: { open: '09:00', close: '20:00', closed: false },\n        friday: { open: '09:00', close: '20:00', closed: false },\n        saturday: { open: '08:00', close: '17:00', closed: false },\n        sunday: { open: '10:00', close: '16:00', closed: false },\n      },\n      defaultMargin: 65\n    },\n    createdAt: new Date('2024-01-15')\n  }\n]\n\n// Mock Users\nexport const mockUsers: User[] = [\n  {\n    id: 'user-1',\n    email: '<EMAIL>',\n    name: '<PERSON>',\n    role: 'owner',\n    organizationId: 'org-1',\n    avatar: '/avatars/maria.jpg',\n    preferences: {\n      theme: 'light',\n      language: 'es',\n      notifications: true,\n      defaultBrands: ['Wella', 'L\\'Oréal', 'Matrix']\n    },\n    createdAt: new Date('2024-01-15')\n  },\n  {\n    id: 'user-2',\n    email: '<EMAIL>',\n    name: 'Sofía Rodríguez',\n    role: 'colorist',\n    organizationId: 'org-1',\n    avatar: '/avatars/sofia.jpg',\n    preferences: {\n      theme: 'light',\n      language: 'es',\n      notifications: true,\n      defaultBrands: ['Wella', 'Schwarzkopf']\n    },\n    createdAt: new Date('2024-02-01')\n  }\n]\n\n// Mock Clients\nexport const mockClients: Client[] = [\n  {\n    id: 'client-1',\n    organizationId: 'org-1',\n    name: 'Laura García',\n    email: '<EMAIL>',\n    phone: '******-0123',\n    birthDate: new Date('1985-03-15'),\n    allergies: [\n      {\n        id: 'allergy-1',\n        name: 'PPD (Para-fenilendiamina)',\n        severity: 'high',\n        notes: 'Reacción severa en prueba anterior'\n      }\n    ],\n    preferences: {\n      preferredBrands: ['Wella', 'Matrix'],\n      avoidIngredients: ['PPD', 'Ammonia'],\n      maxProcessingTime: 60,\n      priceRange: { min: 80, max: 200 }\n    },\n    tags: ['VIP', 'Sensible'],\n    vipStatus: true,\n    photos: [],\n    notes: 'Cliente muy fiel, prefiere tonos fríos. Siempre puntual.',\n    createdAt: new Date('2024-01-20'),\n    lastVisit: new Date('2024-06-15')\n  },\n  {\n    id: 'client-2',\n    organizationId: 'org-1',\n    name: 'Carmen Ruiz',\n    email: '<EMAIL>',\n    phone: '******-0124',\n    birthDate: new Date('1978-08-22'),\n    allergies: [],\n    preferences: {\n      preferredBrands: ['L\\'Oréal'],\n      avoidIngredients: [],\n      maxProcessingTime: 90,\n      priceRange: { min: 100, max: 300 }\n    },\n    tags: ['Regular'],\n    vipStatus: false,\n    photos: [],\n    notes: 'Le gusta experimentar con colores nuevos.',\n    createdAt: new Date('2024-02-10'),\n    lastVisit: new Date('2024-06-10')\n  },\n  {\n    id: 'client-3',\n    organizationId: 'org-1',\n    name: 'Ana Martínez',\n    email: '<EMAIL>',\n    phone: '******-0125',\n    birthDate: new Date('1992-11-05'),\n    allergies: [],\n    preferences: {\n      preferredBrands: ['Matrix', 'Schwarzkopf'],\n      avoidIngredients: [],\n      maxProcessingTime: 75,\n      priceRange: { min: 60, max: 150 }\n    },\n    tags: ['Nueva'],\n    vipStatus: false,\n    photos: [],\n    notes: 'Primera visita muy exitosa, quiere mantener color natural.',\n    createdAt: new Date('2024-06-01'),\n    lastVisit: new Date('2024-06-20')\n  }\n]\n\n// Mock Products\nexport const mockProducts: Product[] = [\n  {\n    id: 'prod-1',\n    organizationId: 'org-1',\n    brand: 'Wella',\n    line: 'Koleston Perfect',\n    name: '6/0 Rubio Oscuro Natural',\n    code: 'WK-60',\n    type: 'color',\n    currentStock: 12,\n    unitCost: 8.50,\n    reorderPoint: 3,\n    properties: {\n      level: 6,\n      tone: 'natural',\n      size: 60,\n      unit: 'ml',\n      ingredients: ['Etanolamina', 'Resorcinol'],\n      ppd: false\n    },\n    createdAt: new Date('2024-01-15')\n  },\n  {\n    id: 'prod-2',\n    organizationId: 'org-1',\n    brand: 'Wella',\n    line: 'Welloxon Perfect',\n    name: 'Revelador 20 Vol',\n    code: 'WW-20',\n    type: 'developer',\n    currentStock: 8,\n    unitCost: 12.00,\n    reorderPoint: 2,\n    properties: {\n      volume: 20,\n      size: 1000,\n      unit: 'ml',\n      ingredients: ['Peróxido de Hidrógeno'],\n      ppd: false\n    },\n    createdAt: new Date('2024-01-15')\n  },\n  {\n    id: 'prod-3',\n    organizationId: 'org-1',\n    brand: 'Matrix',\n    line: 'SoColor',\n    name: '7A Rubio Medio Ceniza',\n    code: 'MS-7A',\n    type: 'color',\n    currentStock: 15,\n    unitCost: 9.25,\n    reorderPoint: 4,\n    properties: {\n      level: 7,\n      tone: 'ash',\n      size: 90,\n      unit: 'ml',\n      ingredients: ['MEA', 'Resorcinol'],\n      ppd: false\n    },\n    createdAt: new Date('2024-01-20')\n  }\n]\n\n// Mock Business Metrics\nexport const mockBusinessMetrics: BusinessMetrics = {\n  period: {\n    start: new Date('2024-06-01'),\n    end: new Date('2024-06-30')\n  },\n  revenue: 12450,\n  services: 89,\n  averageTicket: 139.89,\n  profitMargin: 68.5,\n  clientRetention: 85.2,\n  topServices: [\n    { name: 'Balayage Completo', count: 23, revenue: 4140, margin: 72.1 },\n    { name: 'Retoque de Raíz', count: 31, revenue: 2790, margin: 78.5 },\n    { name: 'Corrección de Color', count: 12, revenue: 2880, margin: 65.3 },\n    { name: 'Mechas Tradicionales', count: 18, revenue: 2340, margin: 69.8 },\n    { name: 'Tinte Completo', count: 5, revenue: 300, margin: 75.0 }\n  ],\n  topProducts: [\n    { name: 'Wella Koleston 6/0', usage: 450, cost: 38.25, efficiency: 92.3 },\n    { name: 'Matrix SoColor 7A', usage: 380, cost: 35.15, efficiency: 89.7 },\n    { name: 'Revelador 20 Vol', usage: 2100, cost: 25.20, efficiency: 95.1 },\n    { name: 'Olaplex No.1', usage: 125, cost: 62.50, efficiency: 88.4 }\n  ]\n}\n\n// Mock Recent Services\nexport const mockRecentServices: Service[] = [\n  {\n    id: 'service-1',\n    clientId: 'client-1',\n    formulaId: 'formula-1',\n    performedBy: 'user-2',\n    scheduledAt: new Date('2024-06-24T10:00:00'),\n    startedAt: new Date('2024-06-24T10:15:00'),\n    completedAt: new Date('2024-06-24T12:30:00'),\n    processNotes: [\n      {\n        time: new Date('2024-06-24T10:15:00'),\n        note: 'Iniciando aplicación en raíz',\n        type: 'system',\n        zone: 'roots'\n      },\n      {\n        time: new Date('2024-06-24T10:45:00'),\n        note: 'Cliente cómoda, sin irritación',\n        type: 'voice'\n      }\n    ],\n    photos: [],\n    totalCharged: 145.00,\n    paymentMethod: 'card',\n    clientRating: 5,\n    clientFeedback: '¡Perfecto como siempre! Me encanta el resultado.',\n    createdAt: new Date('2024-06-24T10:00:00')\n  }\n]\n\n// Helper functions for mock data\nexport function getClientById(id: string): Client | undefined {\n  return mockClients.find(client => client.id === id)\n}\n\nexport function getUserById(id: string): User | undefined {\n  return mockUsers.find(user => user.id === id)\n}\n\nexport function getProductById(id: string): Product | undefined {\n  return mockProducts.find(product => product.id === id)\n}\n\nexport function getClientsByOrganization(orgId: string): Client[] {\n  return mockClients.filter(client => client.organizationId === orgId)\n}\n\nexport function getProductsByOrganization(orgId: string): Product[] {\n  return mockProducts.filter(product => product.organizationId === orgId)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAYO,MAAM,oBAAoC;IAC/C;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,kBAAkB;QAClB,UAAU;YACR,UAAU;YACV,UAAU;YACV,eAAe;gBACb,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACvD,SAAS;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACxD,WAAW;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBAC1D,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACzD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACvD,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;gBACzD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAM;YACzD;YACA,eAAe;QACjB;QACA,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,YAAoB;IAC/B;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,gBAAgB;QAChB,QAAQ;QACR,aAAa;YACX,OAAO;YACP,UAAU;YACV,eAAe;YACf,eAAe;gBAAC;gBAAS;gBAAY;aAAS;QAChD;QACA,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,gBAAgB;QAChB,QAAQ;QACR,aAAa;YACX,OAAO;YACP,UAAU;YACV,eAAe;YACf,eAAe;gBAAC;gBAAS;aAAc;QACzC;QACA,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,cAAwB;IACnC;QACE,IAAI;QACJ,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,OAAO;YACT;SACD;QACD,aAAa;YACX,iBAAiB;gBAAC;gBAAS;aAAS;YACpC,kBAAkB;gBAAC;gBAAO;aAAU;YACpC,mBAAmB;YACnB,YAAY;gBAAE,KAAK;gBAAI,KAAK;YAAI;QAClC;QACA,MAAM;YAAC;YAAO;SAAW;QACzB,WAAW;QACX,QAAQ,EAAE;QACV,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,EAAE;QACb,aAAa;YACX,iBAAiB;gBAAC;aAAW;YAC7B,kBAAkB,EAAE;YACpB,mBAAmB;YACnB,YAAY;gBAAE,KAAK;gBAAK,KAAK;YAAI;QACnC;QACA,MAAM;YAAC;SAAU;QACjB,WAAW;QACX,QAAQ,EAAE;QACV,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,gBAAgB;QAChB,MAAM;QACN,OAAO;QACP,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,EAAE;QACb,aAAa;YACX,iBAAiB;gBAAC;gBAAU;aAAc;YAC1C,kBAAkB,EAAE;YACpB,mBAAmB;YACnB,YAAY;gBAAE,KAAK;gBAAI,KAAK;YAAI;QAClC;QACA,MAAM;YAAC;SAAQ;QACf,WAAW;QACX,QAAQ,EAAE;QACV,OAAO;QACP,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,gBAAgB;QAChB,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,cAAc;QACd,UAAU;QACV,cAAc;QACd,YAAY;YACV,OAAO;YACP,MAAM;YACN,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;gBAAe;aAAa;YAC1C,KAAK;QACP;QACA,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,gBAAgB;QAChB,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,cAAc;QACd,UAAU;QACV,cAAc;QACd,YAAY;YACV,QAAQ;YACR,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;aAAwB;YACtC,KAAK;QACP;QACA,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,gBAAgB;QAChB,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,cAAc;QACd,UAAU;QACV,cAAc;QACd,YAAY;YACV,OAAO;YACP,MAAM;YACN,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;gBAAO;aAAa;YAClC,KAAK;QACP;QACA,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,sBAAuC;IAClD,QAAQ;QACN,OAAO,IAAI,KAAK;QAChB,KAAK,IAAI,KAAK;IAChB;IACA,SAAS;IACT,UAAU;IACV,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,aAAa;QACX;YAAE,MAAM;YAAqB,OAAO;YAAI,SAAS;YAAM,QAAQ;QAAK;QACpE;YAAE,MAAM;YAAmB,OAAO;YAAI,SAAS;YAAM,QAAQ;QAAK;QAClE;YAAE,MAAM;YAAuB,OAAO;YAAI,SAAS;YAAM,QAAQ;QAAK;QACtE;YAAE,MAAM;YAAwB,OAAO;YAAI,SAAS;YAAM,QAAQ;QAAK;QACvE;YAAE,MAAM;YAAkB,OAAO;YAAG,SAAS;YAAK,QAAQ;QAAK;KAChE;IACD,aAAa;QACX;YAAE,MAAM;YAAsB,OAAO;YAAK,MAAM;YAAO,YAAY;QAAK;QACxE;YAAE,MAAM;YAAqB,OAAO;YAAK,MAAM;YAAO,YAAY;QAAK;QACvE;YAAE,MAAM;YAAoB,OAAO;YAAM,MAAM;YAAO,YAAY;QAAK;QACvE;YAAE,MAAM;YAAgB,OAAO;YAAK,MAAM;YAAO,YAAY;QAAK;KACnE;AACH;AAGO,MAAM,qBAAgC;IAC3C;QACE,IAAI;QACJ,UAAU;QACV,WAAW;QACX,aAAa;QACb,aAAa,IAAI,KAAK;QACtB,WAAW,IAAI,KAAK;QACpB,aAAa,IAAI,KAAK;QACtB,cAAc;YACZ;gBACE,MAAM,IAAI,KAAK;gBACf,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM,IAAI,KAAK;gBACf,MAAM;gBACN,MAAM;YACR;SACD;QACD,QAAQ,EAAE;QACV,cAAc;QACd,eAAe;QACf,cAAc;QACd,gBAAgB;QAChB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,SAAS,cAAc,EAAU;IACtC,OAAO,YAAY,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AAClD;AAEO,SAAS,YAAY,EAAU;IACpC,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AAC5C;AAEO,SAAS,eAAe,EAAU;IACvC,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACrD;AAEO,SAAS,yBAAyB,KAAa;IACpD,OAAO,YAAY,MAAM,CAAC,CAAA,SAAU,OAAO,cAAc,KAAK;AAChE;AAEO,SAAS,0BAA0B,KAAa;IACrD,OAAO,aAAa,MAAM,CAAC,CAAA,UAAW,QAAQ,cAAc,KAAK;AACnE", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/lib/auth-context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect } from 'react'\nimport { User, Organization } from '@/types'\nimport { mockUsers, mockOrganizations } from '@/data/mockData'\n\ninterface AuthContextType {\n  user: User | null\n  organization: Organization | null\n  isLoading: boolean\n  login: (email: string, password: string) => Promise<boolean>\n  logout: () => void\n  isAuthenticated: boolean\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [organization, setOrganization] = useState<Organization | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    // Simular carga inicial - verificar si hay usuario guardado\n    const savedUser = localStorage.getItem('salonier-user')\n    if (savedUser) {\n      try {\n        const userData = JSON.parse(savedUser)\n        const foundUser = mockUsers.find(u => u.id === userData.id)\n        if (foundUser) {\n          setUser(foundUser)\n          const userOrg = mockOrganizations.find(org => org.id === foundUser.organizationId)\n          setOrganization(userOrg || null)\n        }\n      } catch (error) {\n        console.error('Error loading saved user:', error)\n        localStorage.removeItem('salonier-user')\n      }\n    }\n    setIsLoading(false)\n  }, [])\n\n  const login = async (email: string, password: string): Promise<boolean> => {\n    setIsLoading(true)\n    \n    // Simular delay de autenticación\n    await new Promise(resolve => setTimeout(resolve, 1000))\n    \n    // Buscar usuario en datos mock\n    const foundUser = mockUsers.find(u => u.email === email)\n    \n    if (foundUser && password === 'demo123') { // Password mock para demo\n      setUser(foundUser)\n      const userOrg = mockOrganizations.find(org => org.id === foundUser.organizationId)\n      setOrganization(userOrg || null)\n      \n      // Guardar en localStorage\n      localStorage.setItem('salonier-user', JSON.stringify({ id: foundUser.id }))\n      \n      setIsLoading(false)\n      return true\n    }\n    \n    setIsLoading(false)\n    return false\n  }\n\n  const logout = () => {\n    setUser(null)\n    setOrganization(null)\n    localStorage.removeItem('salonier-user')\n  }\n\n  const value: AuthContextType = {\n    user,\n    organization,\n    isLoading,\n    login,\n    logout,\n    isAuthenticated: !!user\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\n// Hook para proteger rutas\nexport function useRequireAuth() {\n  const { user, isLoading } = useAuth()\n  \n  useEffect(() => {\n    if (!isLoading && !user) {\n      window.location.href = '/auth/login'\n    }\n  }, [user, isLoading])\n  \n  return { user, isLoading }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;;;AAJA;;;AAeA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,4DAA4D;YAC5D,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,IAAI,WAAW;gBACb,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC;oBAC5B,MAAM,YAAY,0HAAA,CAAA,YAAS,CAAC,IAAI;4DAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,EAAE;;oBAC1D,IAAI,WAAW;wBACb,QAAQ;wBACR,MAAM,UAAU,0HAAA,CAAA,oBAAiB,CAAC,IAAI;8DAAC,CAAA,MAAO,IAAI,EAAE,KAAK,UAAU,cAAc;;wBACjF,gBAAgB,WAAW;oBAC7B;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,6BAA6B;oBAC3C,aAAa,UAAU,CAAC;gBAC1B;YACF;YACA,aAAa;QACf;iCAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAClC,aAAa;QAEb,iCAAiC;QACjC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,+BAA+B;QAC/B,MAAM,YAAY,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QAElD,IAAI,aAAa,aAAa,WAAW;YACvC,QAAQ;YACR,MAAM,UAAU,0HAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,UAAU,cAAc;YACjF,gBAAgB,WAAW;YAE3B,0BAA0B;YAC1B,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;gBAAE,IAAI,UAAU,EAAE;YAAC;YAExE,aAAa;YACb,OAAO;QACT;QAEA,aAAa;QACb,OAAO;IACT;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,gBAAgB;QAChB,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAtEgB;KAAA;AAwET,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,aAAa,CAAC,MAAM;gBACvB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;mCAAG;QAAC;QAAM;KAAU;IAEpB,OAAO;QAAE;QAAM;IAAU;AAC3B;IAVgB;;QACc", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}