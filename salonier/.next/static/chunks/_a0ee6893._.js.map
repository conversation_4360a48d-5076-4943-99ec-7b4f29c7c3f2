{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/lib/auth-context'\n\nexport default function Home() {\n  const { isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (isAuthenticated) {\n        router.push('/dashboard')\n      } else {\n        router.push('/auth/login')\n      }\n    }\n  }, [isAuthenticated, isLoading, router])\n\n  // Mostrar loading mientras se verifica la autenticación\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center\">\n        <div className=\"text-center space-y-4\">\n          <div className=\"mx-auto w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-2xl flex items-center justify-center animate-pulse\">\n            <span className=\"text-2xl font-bold text-white\">S</span>\n          </div>\n          <div>\n            <h1 className=\"text-3xl font-bold gradient-text\">Salonier</h1>\n            <p className=\"text-muted-foreground mt-2\">Cargando...</p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return null\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,WAAW;gBACd,IAAI,iBAAiB;oBACnB,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF;QACF;yBAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,wDAAwD;IACxD,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;kCAElD,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;IAKpD;IAEA,OAAO;AACT;GAhCwB;;QACiB,iIAAA,CAAA,UAAO;QAC/B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Salonier%2025%20Junio/salonier/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}