[{"name": "hot-reloader", "duration": 86, "timestamp": 61350753676, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1750805695739, "traceId": "f6318958275c7bb3"}, {"name": "setup-dev-bundler", "duration": 766498, "timestamp": 61350615654, "id": 2, "parentId": 1, "tags": {}, "startTime": 1750805695601, "traceId": "f6318958275c7bb3"}, {"name": "run-instrumentation-hook", "duration": 11, "timestamp": 61351431319, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750805696417, "traceId": "f6318958275c7bb3"}, {"name": "start-dev-server", "duration": 1097087, "timestamp": 61350343541, "id": 1, "tags": {"cpus": "8", "platform": "darwin", "memory.freeMem": "172212224", "memory.totalMem": "8589934592", "memory.heapSizeLimit": "4345298944", "memory.rss": "248840192", "memory.heapTotal": "97665024", "memory.heapUsed": "75537792"}, "startTime": 1750805695329, "traceId": "f6318958275c7bb3"}, {"name": "compile-path", "duration": 2806906, "timestamp": 61352260782, "id": 7, "tags": {"trigger": "/inventory"}, "startTime": 1750805697246, "traceId": "f6318958275c7bb3"}, {"name": "ensure-page", "duration": 2812750, "timestamp": 61352255551, "id": 6, "parentId": 3, "tags": {"inputPage": "/inventory/page"}, "startTime": 1750805697246, "traceId": "f6318958275c7bb3"}]