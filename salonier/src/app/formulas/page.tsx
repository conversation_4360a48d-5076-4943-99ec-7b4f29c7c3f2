'use client'

import { useState } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Plus, 
  Camera, 
  Palette, 
  Zap,
  Search,
  Filter,
  Clock,
  Star,
  AlertTriangle,
  Eye
} from 'lucide-react'
import { mockClients } from '@/data/mockData'
import Link from 'next/link'

export default function FormulasPage() {
  const [selectedTab, setSelectedTab] = useState('new')

  return (
    <DashboardLayout 
      title="Formulación de Color" 
      subtitle="Análisis IA y generación de fórmulas precisas"
    >
      <div className="space-y-6">
        {/* Tabs de navegación */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
          {[
            { key: 'new', label: 'Nuevo Servicio', icon: Plus },
            { key: 'correction', label: 'Corrección', icon: Zap },
            { key: 'saved', label: 'Fórmulas Guardadas', icon: Palette }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setSelectedTab(tab.key)}
              className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedTab === tab.key
                  ? 'bg-white text-purple-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Contenido según tab seleccionado */}
        {selectedTab === 'new' && <NewServiceFlow />}
        {selectedTab === 'correction' && <CorrectionFlow />}
        {selectedTab === 'saved' && <SavedFormulas />}
      </div>
    </DashboardLayout>
  )
}

function NewServiceFlow() {
  const [step, setStep] = useState(1)
  const [selectedClient, setSelectedClient] = useState<string | null>(null)

  return (
    <div className="space-y-6">
      {/* Indicador de progreso */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {[
            { step: 1, label: 'Cliente', icon: Search },
            { step: 2, label: 'Análisis', icon: Camera },
            { step: 3, label: 'Fórmula', icon: Palette },
            { step: 4, label: 'Proceso', icon: Clock }
          ].map((item) => (
            <div key={item.step} className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                step >= item.step ? 'bg-purple-600 text-white' : 'bg-gray-200 text-gray-600'
              }`}>
                <item.icon className="h-4 w-4" />
              </div>
              <span className={`ml-2 text-sm font-medium ${
                step >= item.step ? 'text-purple-600' : 'text-gray-500'
              }`}>
                {item.label}
              </span>
              {item.step < 4 && (
                <div className={`w-8 h-0.5 ml-4 ${
                  step > item.step ? 'bg-purple-600' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Paso 1: Selección de cliente */}
      {step === 1 && (
        <Card>
          <CardHeader>
            <CardTitle>Seleccionar Cliente</CardTitle>
            <CardDescription>
              Elige el cliente para el nuevo servicio de color
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar cliente por nombre..."
                  className="pl-10"
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                {mockClients.slice(0, 6).map((client) => (
                  <div
                    key={client.id}
                    onClick={() => setSelectedClient(client.id)}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedClient === client.id
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-400 rounded-full flex items-center justify-center">
                        <span className="text-sm font-semibold text-white">
                          {client.name.charAt(0)}
                        </span>
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium">{client.name}</h3>
                        <div className="flex items-center gap-2 mt-1">
                          {client.vipStatus && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              <Star className="h-3 w-3 mr-1" />
                              VIP
                            </span>
                          )}
                          {client.allergies.length > 0 && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              Alergias
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-between pt-4">
                <Link href="/clients">
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Nuevo Cliente
                  </Button>
                </Link>
                <Button 
                  onClick={() => setStep(2)}
                  disabled={!selectedClient}
                >
                  Continuar
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Paso 2: Análisis de color */}
      {step === 2 && (
        <Card>
          <CardHeader>
            <CardTitle>Análisis de Color IA</CardTitle>
            <CardDescription>
              Captura fotos del cabello para análisis automático
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Zona de captura */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {['Raíz', 'Medios', 'Puntas'].map((zone) => (
                  <div key={zone} className="text-center">
                    <div className="w-full h-48 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50 hover:bg-gray-100 cursor-pointer transition-colors">
                      <div className="text-center">
                        <Camera className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm font-medium text-gray-600">{zone}</p>
                        <p className="text-xs text-gray-500">Toca para capturar</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Resultados del análisis (simulado) */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">Análisis IA Completado</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Nivel base:</span> 5 (Castaño claro)
                  </div>
                  <div>
                    <span className="font-medium">Canas:</span> 25%
                  </div>
                  <div>
                    <span className="font-medium">Porosidad:</span> Media
                  </div>
                </div>
              </div>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(1)}>
                  Anterior
                </Button>
                <Button onClick={() => setStep(3)}>
                  Generar Fórmula
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Paso 3: Fórmula generada */}
      {step === 3 && (
        <Card>
          <CardHeader>
            <CardTitle>Fórmula Generada</CardTitle>
            <CardDescription>
              Fórmula optimizada basada en el análisis IA
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Fórmula por zonas */}
              <div className="space-y-4">
                {[
                  { zone: 'Raíz', color: 'Wella 6/0', developer: '20 vol', time: '45 min', cost: '$12.50' },
                  { zone: 'Medios', color: 'Wella 7/1', developer: '20 vol', time: '35 min', cost: '$10.25' },
                  { zone: 'Puntas', color: 'Toner 9/16', developer: '6 vol', time: '20 min', cost: '$8.75' }
                ].map((formula) => (
                  <div key={formula.zone} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{formula.zone}</h4>
                      <span className="text-sm font-medium text-green-600">{formula.cost}</span>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
                      <div>Color: {formula.color}</div>
                      <div>Revelador: {formula.developer}</div>
                      <div>Tiempo: {formula.time}</div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Resumen de costos */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-medium text-green-900">Costo Total de Productos</h4>
                    <p className="text-sm text-green-700">Margen sugerido: 65%</p>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-green-600">$31.50</p>
                    <p className="text-sm text-green-700">Precio sugerido: $90</p>
                  </div>
                </div>
              </div>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep(2)}>
                  Anterior
                </Button>
                <div className="space-x-2">
                  <Button variant="outline">
                    Guardar Fórmula
                  </Button>
                  <Button onClick={() => setStep(4)}>
                    Iniciar Proceso
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Paso 4: Proceso en vivo */}
      {step === 4 && (
        <Card>
          <CardHeader>
            <CardTitle>Proceso en Vivo</CardTitle>
            <CardDescription>
              Timers y documentación automática del servicio
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <Clock className="h-16 w-16 text-purple-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Proceso Iniciado</h3>
              <p className="text-gray-600 mb-6">
                Los timers y la documentación automática estarán disponibles en la siguiente versión.
              </p>
              <Button onClick={() => setStep(1)}>
                Nuevo Servicio
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

function CorrectionFlow() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Zap className="h-5 w-5 mr-2 text-orange-500" />
          Modo Corrección Express
        </CardTitle>
        <CardDescription>
          Análisis rápido y soluciones para correcciones de color
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center py-12">
          <Zap className="h-16 w-16 text-orange-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">Corrección Express</h3>
          <p className="text-gray-600 mb-6">
            El modo de corrección express estará disponible en la próxima actualización.
          </p>
          <Button variant="outline">
            Próximamente
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

function SavedFormulas() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Palette className="h-5 w-5 mr-2" />
          Fórmulas Guardadas
        </CardTitle>
        <CardDescription>
          Biblioteca de fórmulas exitosas y plantillas
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center py-12">
          <Palette className="h-16 w-16 text-purple-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">Sin fórmulas guardadas</h3>
          <p className="text-gray-600 mb-6">
            Las fórmulas que guardes aparecerán aquí para reutilizar.
          </p>
          <Button variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            Crear Primera Fórmula
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
