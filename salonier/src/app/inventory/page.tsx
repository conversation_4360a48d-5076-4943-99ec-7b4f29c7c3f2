'use client'

import { useState } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Search, 
  Plus, 
  Filter,
  Package,
  AlertTriangle,
  TrendingDown,
  TrendingUp,
  BarChart3,
  ShoppingCart,
  Edit,
  Trash2
} from 'lucide-react'
import { mockProducts } from '@/data/mockData'
import { formatCurrency } from '@/lib/utils'

export default function InventoryPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFilter, setSelectedFilter] = useState('all')
  const [selectedView, setSelectedView] = useState('grid')

  const filteredProducts = mockProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.code.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = selectedFilter === 'all' || 
                         (selectedFilter === 'low_stock' && product.currentStock <= product.reorderPoint) ||
                         (selectedFilter === 'color' && product.type === 'color') ||
                         (selectedFilter === 'developer' && product.type === 'developer')
    
    return matchesSearch && matchesFilter
  })

  const lowStockCount = mockProducts.filter(p => p.currentStock <= p.reorderPoint).length
  const totalValue = mockProducts.reduce((sum, p) => sum + (p.currentStock * p.unitCost), 0)

  return (
    <DashboardLayout 
      title="Gestión de Inventario" 
      subtitle="Control de stock, costos y análisis de rentabilidad"
    >
      <div className="space-y-6">
        {/* Métricas del inventario */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Productos</p>
                  <p className="text-2xl font-bold text-gray-900">{mockProducts.length}</p>
                </div>
                <Package className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Valor Total</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalValue)}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Stock Bajo</p>
                  <p className="text-2xl font-bold text-red-600">{lowStockCount}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Rotación</p>
                  <p className="text-2xl font-bold text-purple-600">85%</p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Controles */}
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Buscar productos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filtros
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Nuevo Producto
            </Button>
          </div>
        </div>

        {/* Filtros rápidos */}
        <div className="flex gap-2 flex-wrap">
          {[
            { key: 'all', label: 'Todos', count: mockProducts.length },
            { key: 'low_stock', label: 'Stock Bajo', count: lowStockCount },
            { key: 'color', label: 'Colores', count: mockProducts.filter(p => p.type === 'color').length },
            { key: 'developer', label: 'Reveladores', count: mockProducts.filter(p => p.type === 'developer').length }
          ].map((filter) => (
            <Button
              key={filter.key}
              variant={selectedFilter === filter.key ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedFilter(filter.key)}
            >
              {filter.label} ({filter.count})
            </Button>
          ))}
        </div>

        {/* Lista de productos */}
        <Card>
          <CardHeader>
            <CardTitle>Productos en Inventario</CardTitle>
            <CardDescription>
              Gestiona tu stock y controla los costos de productos
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredProducts.map((product) => (
                <div key={product.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-4 flex-1">
                    {/* Indicador de tipo */}
                    <div className={`w-3 h-3 rounded-full ${
                      product.type === 'color' ? 'bg-purple-500' :
                      product.type === 'developer' ? 'bg-blue-500' :
                      'bg-gray-500'
                    }`} />
                    
                    {/* Información del producto */}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-medium text-gray-900">{product.name}</h3>
                        <span className="text-sm text-gray-500">({product.code})</span>
                        {product.currentStock <= product.reorderPoint && (
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                        )}
                      </div>
                      <p className="text-sm text-gray-600">{product.brand} - {product.line}</p>
                      <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                        <span>Tamaño: {product.properties.size}{product.properties.unit}</span>
                        {product.properties.level && (
                          <span>Nivel: {product.properties.level}</span>
                        )}
                        {product.properties.tone && (
                          <span>Tono: {product.properties.tone}</span>
                        )}
                        {product.properties.volume && (
                          <span>Volumen: {product.properties.volume}%</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Stock y costos */}
                  <div className="flex items-center space-x-6">
                    <div className="text-center">
                      <p className="text-sm font-medium text-gray-900">Stock</p>
                      <p className={`text-lg font-bold ${
                        product.currentStock <= product.reorderPoint ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {product.currentStock}
                      </p>
                      <p className="text-xs text-gray-500">Min: {product.reorderPoint}</p>
                    </div>

                    <div className="text-center">
                      <p className="text-sm font-medium text-gray-900">Costo Unit.</p>
                      <p className="text-lg font-bold text-gray-900">
                        {formatCurrency(product.unitCost)}
                      </p>
                    </div>

                    <div className="text-center">
                      <p className="text-sm font-medium text-gray-900">Valor Total</p>
                      <p className="text-lg font-bold text-purple-600">
                        {formatCurrency(product.currentStock * product.unitCost)}
                      </p>
                    </div>

                    {/* Acciones */}
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <ShoppingCart className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredProducts.length === 0 && (
              <div className="text-center py-12">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No se encontraron productos
                </h3>
                <p className="text-gray-500 mb-4">
                  {searchTerm ? 
                    `No hay productos que coincidan con "${searchTerm}"` :
                    'No hay productos en esta categoría'
                  }
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Agregar Producto
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Alertas de stock bajo */}
        {lowStockCount > 0 && (
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="flex items-center text-red-800">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Alertas de Stock Bajo
              </CardTitle>
              <CardDescription className="text-red-700">
                {lowStockCount} productos necesitan reabastecimiento
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {mockProducts
                  .filter(p => p.currentStock <= p.reorderPoint)
                  .map((product) => (
                    <div key={product.id} className="flex items-center justify-between p-3 bg-white rounded border border-red-200">
                      <div>
                        <span className="font-medium text-red-900">{product.name}</span>
                        <span className="text-sm text-red-700 ml-2">
                          Stock: {product.currentStock} (Min: {product.reorderPoint})
                        </span>
                      </div>
                      <Button size="sm" variant="outline">
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        Reabastecer
                      </Button>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
