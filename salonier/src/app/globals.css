@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

:root {
  /* Salonier Color Palette */
  --primary: #6B46C1;
  --primary-foreground: #FFFFFF;
  --secondary: #E8B4B8;
  --secondary-foreground: #1A1A1A;
  --accent: #FAFAF9;
  --accent-foreground: #1A1A1A;

  /* Neutral Colors */
  --background: #FAFAF9;
  --foreground: #1A1A1A;
  --muted: #F5F5F5;
  --muted-foreground: #64646A;
  --border: #E5E5E5;
  --input: #FFFFFF;
  --ring: #6B46C1;

  /* Semantic Colors */
  --success: #10B981;
  --success-foreground: #FFFFFF;
  --warning: #F59E0B;
  --warning-foreground: #FFFFFF;
  --destructive: #EF4444;
  --destructive-foreground: #FFFFFF;

  /* Card and Surface */
  --card: #FFFFFF;
  --card-foreground: #1A1A1A;
  --popover: #FFFFFF;
  --popover-foreground: #1A1A1A;

  /* Radius */
  --radius: 0.75rem;
}

[data-theme="dark"] {
  --background: #0A0A0A;
  --foreground: #FAFAF9;
  --muted: #1A1A1A;
  --muted-foreground: #A0A0A0;
  --border: #2A2A2A;
  --input: #1A1A1A;
  --card: #1A1A1A;
  --card-foreground: #FAFAF9;
  --popover: #1A1A1A;
  --popover-foreground: #FAFAF9;
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'rlig' 1, 'calt' 1;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  line-height: 1.2;
}

.font-display {
  font-family: 'Playfair Display', serif;
}

/* Simplified styles without @layer to avoid conflicts */
.gradient-text {
  background: linear-gradient(to right, #6B46C1, #E8B4B8);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Utility classes */
.text-balance {
  text-wrap: balance;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

.animation-delay-600 {
  animation-delay: 600ms;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}
