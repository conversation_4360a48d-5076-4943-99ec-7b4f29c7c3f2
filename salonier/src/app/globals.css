@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: 107 70 193;
  --primary-foreground: 255 255 255;
  --secondary: 232 180 184;
  --secondary-foreground: 26 26 26;
  --background: 250 250 249;
  --foreground: 26 26 26;
  --muted: 245 245 245;
  --muted-foreground: 100 100 106;
  --border: 229 229 229;
  --input: 255 255 255;
  --ring: 107 70 193;
  --success: 16 185 129;
  --warning: 245 158 11;
  --destructive: 239 68 68;
  --card: 255 255 255;
  --card-foreground: 26 26 26;
  --radius: 0.75rem;
}

[data-theme="dark"] {
  --background: 10 10 10;
  --foreground: 250 250 249;
  --muted: 26 26 26;
  --muted-foreground: 160 160 160;
  --border: 42 42 42;
  --input: 26 26 26;
  --card: 26 26 26;
  --card-foreground: 250 250 249;
}

body {
  background-color: rgb(var(--background));
  color: rgb(var(--foreground));
  font-family: 'Inter', system-ui, sans-serif;
}

.gradient-text {
  background: linear-gradient(to right, rgb(107 70 193), rgb(232 180 184));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
