'use client'

import { useState } from 'react'
import { useParams } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  ArrowLeft,
  Edit,
  Phone,
  Mail,
  Calendar,
  Star,
  AlertTriangle,
  Camera,
  Clock,
  DollarSign,
  Palette
} from 'lucide-react'
import { mockClients, mockRecentServices, getUserById } from '@/data/mockData'
import { timeAgo, formatCurrency } from '@/lib/utils'
import Link from 'next/link'

export default function ClientProfilePage() {
  const params = useParams()
  const clientId = params.id as string
  
  const client = mockClients.find(c => c.id === clientId)
  const clientServices = mockRecentServices.filter(s => s.clientId === clientId)

  if (!client) {
    return (
      <DashboardLayout title="Cliente no encontrado">
        <Card>
          <CardContent className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Cliente no encontrado
            </h3>
            <p className="text-gray-500 mb-4">
              El cliente que buscas no existe o ha sido eliminado.
            </p>
            <Link href="/clients">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Volver a Clientes
              </Button>
            </Link>
          </CardContent>
        </Card>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout 
      title={client.name}
      subtitle="Perfil completo del cliente"
    >
      <div className="space-y-6">
        {/* Header con navegación */}
        <div className="flex items-center justify-between">
          <Link href="/clients">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Volver a Clientes
            </Button>
          </Link>
          
          <div className="flex gap-2">
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Editar
            </Button>
            <Button>
              <Palette className="h-4 w-4 mr-2" />
              Nuevo Servicio
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Información del cliente */}
          <div className="lg:col-span-1 space-y-6">
            {/* Perfil básico */}
            <Card>
              <CardHeader className="text-center">
                <div className="mx-auto w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-400 rounded-full flex items-center justify-center mb-4">
                  <span className="text-3xl font-bold text-white">
                    {client.name.charAt(0)}
                  </span>
                </div>
                <CardTitle className="text-2xl">{client.name}</CardTitle>
                <div className="flex items-center justify-center gap-2 mt-2">
                  {client.vipStatus && (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                      <Star className="h-4 w-4 mr-1" />
                      Cliente VIP
                    </span>
                  )}
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Información de contacto */}
                <div className="space-y-3">
                  {client.email && (
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 mr-3 text-gray-400" />
                      <span className="text-sm">{client.email}</span>
                    </div>
                  )}
                  {client.phone && (
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-3 text-gray-400" />
                      <span className="text-sm">{client.phone}</span>
                    </div>
                  )}
                  {client.birthDate && (
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-3 text-gray-400" />
                      <span className="text-sm">
                        {client.birthDate.toLocaleDateString('es-ES')}
                      </span>
                    </div>
                  )}
                </div>

                {/* Estadísticas rápidas */}
                <div className="pt-4 border-t">
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <p className="text-2xl font-bold text-purple-600">{clientServices.length}</p>
                      <p className="text-xs text-gray-500">Servicios</p>
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-green-600">
                        {formatCurrency(clientServices.reduce((sum, s) => sum + s.totalCharged, 0))}
                      </p>
                      <p className="text-xs text-gray-500">Total gastado</p>
                    </div>
                  </div>
                </div>

                {/* Última visita */}
                <div className="pt-4 border-t">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Última visita:</span>
                    <span className="text-sm text-gray-600">
                      {client.lastVisit ? timeAgo(client.lastVisit) : 'Nunca'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Alergias y restricciones */}
            {client.allergies.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-red-600">
                    <AlertTriangle className="h-5 w-5 mr-2" />
                    Alergias y Restricciones
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {client.allergies.map((allergy) => (
                      <div key={allergy.id} className="p-3 bg-red-50 rounded-lg border border-red-200">
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium text-red-800">{allergy.name}</span>
                          <span className={`px-2 py-1 rounded text-xs font-medium ${
                            allergy.severity === 'high' ? 'bg-red-200 text-red-800' :
                            allergy.severity === 'medium' ? 'bg-yellow-200 text-yellow-800' : 
                            'bg-green-200 text-green-800'
                          }`}>
                            {allergy.severity === 'high' ? 'Severidad Alta' :
                             allergy.severity === 'medium' ? 'Severidad Media' : 'Severidad Baja'}
                          </span>
                        </div>
                        {allergy.notes && (
                          <p className="text-sm text-red-700">{allergy.notes}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Tags */}
            {client.tags.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Etiquetas</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {client.tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Historial y servicios */}
          <div className="lg:col-span-2 space-y-6">
            {/* Historial de servicios */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Historial de Servicios
                </CardTitle>
                <CardDescription>
                  Todos los servicios realizados a este cliente
                </CardDescription>
              </CardHeader>
              <CardContent>
                {clientServices.length > 0 ? (
                  <div className="space-y-4">
                    {clientServices.map((service) => {
                      const stylist = getUserById(service.performedBy)
                      return (
                        <div key={service.id} className="p-4 border rounded-lg hover:bg-gray-50">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <span className="font-medium">Servicio de Color</span>
                                {service.clientRating && (
                                  <div className="flex items-center">
                                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                                    <span className="text-sm text-gray-600 ml-1">
                                      {service.clientRating}/5
                                    </span>
                                  </div>
                                )}
                              </div>
                              <p className="text-sm text-gray-600 mb-2">
                                Realizado por {stylist?.name} • {timeAgo(service.scheduledAt)}
                              </p>
                              {service.clientFeedback && (
                                <p className="text-sm text-gray-700 italic">
                                  "{service.clientFeedback}"
                                </p>
                              )}
                            </div>
                            <div className="text-right">
                              <p className="font-semibold text-green-600">
                                {formatCurrency(service.totalCharged)}
                              </p>
                              <p className="text-xs text-gray-500">
                                {service.paymentMethod === 'card' ? 'Tarjeta' : 'Efectivo'}
                              </p>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Sin historial de servicios
                    </h3>
                    <p className="text-gray-500 mb-4">
                      Este cliente aún no tiene servicios registrados.
                    </p>
                    <Button>
                      <Palette className="h-4 w-4 mr-2" />
                      Crear Primer Servicio
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Galería de fotos */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Camera className="h-5 w-5 mr-2" />
                  Galería de Fotos
                </CardTitle>
                <CardDescription>
                  Fotos de antes y después de los servicios
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Sin fotos
                  </h3>
                  <p className="text-gray-500 mb-4">
                    Las fotos de los servicios aparecerán aquí.
                  </p>
                  <Button variant="outline">
                    <Camera className="h-4 w-4 mr-2" />
                    Subir Fotos
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Notas */}
            <Card>
              <CardHeader>
                <CardTitle>Notas del Cliente</CardTitle>
              </CardHeader>
              <CardContent>
                {client.notes ? (
                  <p className="text-gray-700">{client.notes}</p>
                ) : (
                  <p className="text-gray-500 italic">Sin notas adicionales</p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
