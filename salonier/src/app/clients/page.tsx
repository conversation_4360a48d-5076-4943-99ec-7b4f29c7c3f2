'use client'

import { useState } from 'react'
import Link from 'next/link'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Search,
  Plus,
  Filter,
  Star,
  Phone,
  Mail,
  Calendar,
  AlertTriangle
} from 'lucide-react'
import { mockClients } from '@/data/mockData'
import { timeAgo } from '@/lib/utils'

export default function ClientsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFilter, setSelectedFilter] = useState('all')

  const filteredClients = mockClients.filter(client => {
    const matchesSearch = client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.email?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = selectedFilter === 'all' || 
                         (selectedFilter === 'vip' && client.vipStatus) ||
                         (selectedFilter === 'new' && !client.lastVisit) ||
                         (selectedFilter === 'allergies' && client.allergies.length > 0)
    
    return matchesSearch && matchesFilter
  })

  return (
    <DashboardLayout 
      title="Gestión de Clientes" 
      subtitle="Administra tu cartera de clientes y su historial"
    >
      <div className="space-y-6">
        {/* Header con búsqueda y filtros */}
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Buscar clientes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filtros
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Nuevo Cliente
            </Button>
          </div>
        </div>

        {/* Filtros rápidos */}
        <div className="flex gap-2 flex-wrap">
          {[
            { key: 'all', label: 'Todos', count: mockClients.length },
            { key: 'vip', label: 'VIP', count: mockClients.filter(c => c.vipStatus).length },
            { key: 'new', label: 'Nuevos', count: mockClients.filter(c => !c.lastVisit).length },
            { key: 'allergies', label: 'Con Alergias', count: mockClients.filter(c => c.allergies.length > 0).length }
          ].map((filter) => (
            <Button
              key={filter.key}
              variant={selectedFilter === filter.key ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedFilter(filter.key)}
            >
              {filter.label} ({filter.count})
            </Button>
          ))}
        </div>

        {/* Lista de clientes */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredClients.map((client) => (
            <Link key={client.id} href={`/clients/${client.id}`}>
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-400 rounded-full flex items-center justify-center">
                      <span className="text-lg font-semibold text-white">
                        {client.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <CardTitle className="text-lg">{client.name}</CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        {client.vipStatus && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <Star className="h-3 w-3 mr-1" />
                            VIP
                          </span>
                        )}
                        {client.allergies.length > 0 && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            Alergias
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-3">
                {/* Información de contacto */}
                <div className="space-y-2">
                  {client.email && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Mail className="h-4 w-4 mr-2" />
                      {client.email}
                    </div>
                  )}
                  {client.phone && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Phone className="h-4 w-4 mr-2" />
                      {client.phone}
                    </div>
                  )}
                </div>

                {/* Última visita */}
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  {client.lastVisit ? (
                    <span>Última visita: {timeAgo(client.lastVisit)}</span>
                  ) : (
                    <span className="text-blue-600 font-medium">Cliente nuevo</span>
                  )}
                </div>

                {/* Tags */}
                {client.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {client.tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}

                {/* Alergias */}
                {client.allergies.length > 0 && (
                  <div className="pt-2 border-t">
                    <p className="text-xs font-medium text-red-600 mb-1">Alergias:</p>
                    <div className="space-y-1">
                      {client.allergies.map((allergy) => (
                        <div key={allergy.id} className="text-xs text-red-700">
                          • {allergy.name} 
                          <span className={`ml-1 px-1 rounded text-xs ${
                            allergy.severity === 'high' ? 'bg-red-200' :
                            allergy.severity === 'medium' ? 'bg-yellow-200' : 'bg-green-200'
                          }`}>
                            {allergy.severity === 'high' ? 'Alta' :
                             allergy.severity === 'medium' ? 'Media' : 'Baja'}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Acciones */}
                <div className="flex gap-2 pt-3">
                  <Button variant="outline" size="sm" className="flex-1">
                    Ver Perfil
                  </Button>
                  <Button size="sm" className="flex-1">
                    Nuevo Servicio
                  </Button>
                </div>
              </CardContent>
            </Card>
            </Link>
          ))}
        </div>

        {/* Estado vacío */}
        {filteredClients.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Search className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No se encontraron clientes
              </h3>
              <p className="text-gray-500 mb-4">
                {searchTerm ? 
                  `No hay clientes que coincidan con "${searchTerm}"` :
                  'No hay clientes en esta categoría'
                }
              </p>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Agregar Primer Cliente
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  )
}
