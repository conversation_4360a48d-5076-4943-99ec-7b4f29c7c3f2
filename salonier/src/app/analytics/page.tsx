'use client'

import { useState } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Users,
  Palette,
  Calendar,
  Download,
  Filter,
  BarChart3,
  PieChart,
  Target
} from 'lucide-react'
import { mockBusinessMetrics } from '@/data/mockData'
import { formatCurrency, formatPercentage } from '@/lib/utils'

export default function AnalyticsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [selectedView, setSelectedView] = useState('overview')

  const metrics = mockBusinessMetrics

  return (
    <DashboardLayout 
      title="Analytics y Reportes" 
      subtitle="Análisis de rendimiento y métricas de negocio"
    >
      <div className="space-y-6">
        {/* Controles de período */}
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex gap-2">
            {[
              { key: 'week', label: 'Esta Semana' },
              { key: 'month', label: 'Este Mes' },
              { key: 'quarter', label: 'Trimestre' },
              { key: 'year', label: 'Año' }
            ].map((period) => (
              <Button
                key={period.key}
                variant={selectedPeriod === period.key ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedPeriod(period.key)}
              >
                {period.label}
              </Button>
            ))}
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filtros
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </Button>
          </div>
        </div>

        {/* Métricas principales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Ingresos</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(metrics.revenue)}
                  </p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                    <span className="text-sm text-green-600 font-medium">+12.5%</span>
                    <span className="text-sm text-gray-500 ml-1">vs mes anterior</span>
                  </div>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Servicios</p>
                  <p className="text-2xl font-bold text-gray-900">{metrics.services}</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                    <span className="text-sm text-green-600 font-medium">+8.2%</span>
                    <span className="text-sm text-gray-500 ml-1">vs mes anterior</span>
                  </div>
                </div>
                <Palette className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Ticket Promedio</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(metrics.averageTicket)}
                  </p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                    <span className="text-sm text-green-600 font-medium">+5.1%</span>
                    <span className="text-sm text-gray-500 ml-1">vs mes anterior</span>
                  </div>
                </div>
                <Target className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Margen de Ganancia</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPercentage(metrics.profitMargin)}
                  </p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                    <span className="text-sm text-green-600 font-medium">+2.3%</span>
                    <span className="text-sm text-gray-500 ml-1">vs mes anterior</span>
                  </div>
                </div>
                <BarChart3 className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Servicios más populares */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Palette className="h-5 w-5 mr-2" />
                Servicios Más Populares
              </CardTitle>
              <CardDescription>
                Ranking de servicios por ingresos y frecuencia
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {metrics.topServices.map((service, index) => (
                  <div key={service.name} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-purple-600">#{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{service.name}</p>
                        <p className="text-sm text-gray-600">{service.count} servicios</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-green-600">
                        {formatCurrency(service.revenue)}
                      </p>
                      <p className="text-sm text-gray-600">
                        {formatPercentage(service.margin)} margen
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Productos más utilizados */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Productos Más Utilizados
              </CardTitle>
              <CardDescription>
                Análisis de consumo y eficiencia de productos
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {metrics.topProducts.map((product, index) => (
                  <div key={product.name} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-blue-600">#{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{product.name}</p>
                        <p className="text-sm text-gray-600">{product.usage}ml utilizados</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">
                        {formatCurrency(product.cost)}
                      </p>
                      <p className="text-sm text-green-600">
                        {formatPercentage(product.efficiency)} eficiencia
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Gráficos de tendencias */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Tendencia de Ingresos</CardTitle>
              <CardDescription>
                Evolución de ingresos en los últimos 6 meses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">Gráfico de tendencias</p>
                  <p className="text-sm text-gray-500">Disponible en la próxima versión</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Distribución de Servicios</CardTitle>
              <CardDescription>
                Proporción de tipos de servicios realizados
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center">
                  <PieChart className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">Gráfico circular</p>
                  <p className="text-sm text-gray-500">Disponible en la próxima versión</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Métricas de retención */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Análisis de Clientes
            </CardTitle>
            <CardDescription>
              Métricas de retención y satisfacción de clientes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-6 bg-green-50 rounded-lg">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <p className="text-2xl font-bold text-green-600">
                  {formatPercentage(metrics.clientRetention)}
                </p>
                <p className="text-sm text-green-700 font-medium">Retención de Clientes</p>
                <p className="text-xs text-green-600 mt-1">+2.3% vs mes anterior</p>
              </div>

              <div className="text-center p-6 bg-blue-50 rounded-lg">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Calendar className="h-6 w-6 text-blue-600" />
                </div>
                <p className="text-2xl font-bold text-blue-600">28</p>
                <p className="text-sm text-blue-700 font-medium">Días Promedio Entre Visitas</p>
                <p className="text-xs text-blue-600 mt-1">-3 días vs mes anterior</p>
              </div>

              <div className="text-center p-6 bg-yellow-50 rounded-lg">
                <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Target className="h-6 w-6 text-yellow-600" />
                </div>
                <p className="text-2xl font-bold text-yellow-600">4.8</p>
                <p className="text-sm text-yellow-700 font-medium">Satisfacción Promedio</p>
                <p className="text-xs text-yellow-600 mt-1">+0.2 vs mes anterior</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Objetivos y metas */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="h-5 w-5 mr-2" />
              Objetivos del Mes
            </CardTitle>
            <CardDescription>
              Progreso hacia las metas establecidas
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { goal: 'Ingresos Mensuales', target: 15000, current: 12450, unit: '$' },
                { goal: 'Servicios Realizados', target: 100, current: 89, unit: '' },
                { goal: 'Nuevos Clientes', target: 15, current: 12, unit: '' },
                { goal: 'Retención de Clientes', target: 90, current: 85.2, unit: '%' }
              ].map((objective) => {
                const progress = (objective.current / objective.target) * 100
                return (
                  <div key={objective.goal} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-medium text-gray-900">{objective.goal}</span>
                      <span className="text-sm text-gray-600">
                        {objective.unit === '$' ? formatCurrency(objective.current) : `${objective.current}${objective.unit}`} / 
                        {objective.unit === '$' ? formatCurrency(objective.target) : `${objective.target}${objective.unit}`}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          progress >= 100 ? 'bg-green-500' :
                          progress >= 75 ? 'bg-blue-500' :
                          progress >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${Math.min(progress, 100)}%` }}
                      />
                    </div>
                    <div className="text-right">
                      <span className={`text-sm font-medium ${
                        progress >= 100 ? 'text-green-600' :
                        progress >= 75 ? 'text-blue-600' :
                        progress >= 50 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {progress.toFixed(1)}% completado
                      </span>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
