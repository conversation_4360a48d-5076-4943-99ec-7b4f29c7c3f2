'use client'

import { useState } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Plus, 
  Clock, 
  Play,
  Pause,
  Square,
  Timer,
  Camera,
  Mic,
  Search,
  Filter,
  Calendar,
  User,
  Palette,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { mockRecentServices, mockClients, getUserById } from '@/data/mockData'
import { formatCurrency, timeAgo } from '@/lib/utils'
import Link from 'next/link'

export default function ServicesPage() {
  const [selectedTab, setSelectedTab] = useState('active')
  const [activeTimers, setActiveTimers] = useState<any[]>([])

  return (
    <DashboardLayout 
      title="Gestión de Servicios" 
      subtitle="Control de servicios activos, timers y documentación"
    >
      <div className="space-y-6">
        {/* Tabs de navegación */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
          {[
            { key: 'active', label: 'Servicios Activos', icon: Play },
            { key: 'scheduled', label: 'Programados', icon: Calendar },
            { key: 'completed', label: 'Completados', icon: CheckCircle },
            { key: 'new', label: 'Nuevo Servicio', icon: Plus }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setSelectedTab(tab.key)}
              className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedTab === tab.key
                  ? 'bg-white text-purple-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Contenido según tab seleccionado */}
        {selectedTab === 'active' && <ActiveServices />}
        {selectedTab === 'scheduled' && <ScheduledServices />}
        {selectedTab === 'completed' && <CompletedServices />}
        {selectedTab === 'new' && <NewServiceForm />}
      </div>
    </DashboardLayout>
  )
}

function ActiveServices() {
  const [timers, setTimers] = useState([
    {
      id: '1',
      clientName: 'Laura García',
      service: 'Balayage Completo',
      zone: 'Raíz',
      startTime: new Date(Date.now() - 25 * 60 * 1000), // 25 minutos atrás
      duration: 45,
      status: 'running'
    },
    {
      id: '2',
      clientName: 'Carmen Ruiz',
      service: 'Retoque de Color',
      zone: 'Medios',
      startTime: new Date(Date.now() - 15 * 60 * 1000), // 15 minutos atrás
      duration: 30,
      status: 'running'
    }
  ])

  const toggleTimer = (id: string) => {
    setTimers(timers.map(timer => 
      timer.id === id 
        ? { ...timer, status: timer.status === 'running' ? 'paused' : 'running' }
        : timer
    ))
  }

  const stopTimer = (id: string) => {
    setTimers(timers.filter(timer => timer.id !== id))
  }

  return (
    <div className="space-y-6">
      {timers.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {timers.map((timer) => {
            const elapsed = Math.floor((Date.now() - timer.startTime.getTime()) / 1000 / 60)
            const remaining = timer.duration - elapsed
            const progress = (elapsed / timer.duration) * 100

            return (
              <Card key={timer.id} className="border-l-4 border-l-purple-500">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{timer.clientName}</CardTitle>
                      <CardDescription>{timer.service} - {timer.zone}</CardDescription>
                    </div>
                    <div className={`w-3 h-3 rounded-full ${
                      timer.status === 'running' ? 'bg-green-500 animate-pulse' : 'bg-yellow-500'
                    }`} />
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Timer display */}
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600 mb-1">
                      {Math.floor(remaining / 60)}:{(remaining % 60).toString().padStart(2, '0')}
                    </div>
                    <p className="text-sm text-gray-600">
                      {remaining > 0 ? 'Tiempo restante' : 'Tiempo excedido'}
                    </p>
                  </div>

                  {/* Progress bar */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>Progreso</span>
                      <span>{Math.min(progress, 100).toFixed(0)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-1000 ${
                          progress >= 100 ? 'bg-red-500' : 'bg-purple-500'
                        }`}
                        style={{ width: `${Math.min(progress, 100)}%` }}
                      />
                    </div>
                  </div>

                  {/* Controls */}
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleTimer(timer.id)}
                      className="flex-1"
                    >
                      {timer.status === 'running' ? (
                        <>
                          <Pause className="h-4 w-4 mr-2" />
                          Pausar
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-2" />
                          Reanudar
                        </>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => stopTimer(timer.id)}
                    >
                      <Square className="h-4 w-4 mr-2" />
                      Finalizar
                    </Button>
                  </div>

                  {/* Quick actions */}
                  <div className="flex gap-2 pt-2 border-t">
                    <Button variant="ghost" size="sm" className="flex-1">
                      <Camera className="h-4 w-4 mr-2" />
                      Foto
                    </Button>
                    <Button variant="ghost" size="sm" className="flex-1">
                      <Mic className="h-4 w-4 mr-2" />
                      Nota
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Timer className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">No hay servicios activos</h3>
            <p className="text-gray-600 mb-6">
              Inicia un nuevo servicio para comenzar a usar los timers inteligentes.
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Nuevo Servicio
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

function ScheduledServices() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Calendar className="h-5 w-5 mr-2" />
          Servicios Programados
        </CardTitle>
        <CardDescription>
          Citas y servicios agendados para los próximos días
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center py-12">
          <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">No hay servicios programados</h3>
          <p className="text-gray-600 mb-6">
            Programa citas para organizar mejor tu agenda.
          </p>
          <Button variant="outline">
            <Plus className="h-4 w-4 mr-2" />
            Programar Cita
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

function CompletedServices() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <CheckCircle className="h-5 w-5 mr-2" />
          Servicios Completados
        </CardTitle>
        <CardDescription>
          Historial de servicios realizados recientemente
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {mockRecentServices.map((service) => {
            const client = mockClients.find(c => c.id === service.clientId)
            const stylist = getUserById(service.performedBy)
            
            return (
              <div key={service.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-400 rounded-full flex items-center justify-center">
                    <span className="text-sm font-semibold text-white">
                      {client?.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{client?.name}</h3>
                    <p className="text-sm text-gray-600">
                      Servicio de Color • {stylist?.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {timeAgo(service.scheduledAt)}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="font-semibold text-green-600">
                      {formatCurrency(service.totalCharged)}
                    </p>
                    {service.clientRating && (
                      <div className="flex items-center">
                        <span className="text-sm text-yellow-500">★</span>
                        <span className="text-sm text-gray-600 ml-1">
                          {service.clientRating}/5
                        </span>
                      </div>
                    )}
                  </div>
                  
                  <Button variant="outline" size="sm">
                    Ver Detalles
                  </Button>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}

function NewServiceForm() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Plus className="h-5 w-5 mr-2" />
          Nuevo Servicio
        </CardTitle>
        <CardDescription>
          Inicia un nuevo servicio con análisis IA y timers automáticos
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center py-12">
          <Palette className="h-16 w-16 text-purple-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">Crear Nuevo Servicio</h3>
          <p className="text-gray-600 mb-6">
            Ve a la sección de Formulación para crear un servicio completo con análisis IA.
          </p>
          <Link href="/formulas">
            <Button>
              <Palette className="h-4 w-4 mr-2" />
              Ir a Formulación
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
