// User and Authentication Types
export interface User {
  id: string
  email: string
  name: string
  role: 'owner' | 'admin' | 'colorist' | 'assistant'
  organizationId: string
  avatar?: string
  preferences: UserPreferences
  createdAt: Date
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto'
  language: 'es' | 'en'
  notifications: boolean
  defaultBrands: string[]
}

export interface Organization {
  id: string
  name: string
  type: 'individual' | 'salon' | 'chain'
  subscriptionTier: 'free' | 'pro' | 'studio' | 'enterprise'
  settings: OrganizationSettings
  createdAt: Date
}

export interface OrganizationSettings {
  currency: string
  timezone: string
  businessHours: BusinessHours
  defaultMargin: number
}

export interface BusinessHours {
  [key: string]: { open: string; close: string; closed: boolean }
}

// Client Types
export interface Client {
  id: string
  organizationId: string
  name: string
  email?: string
  phone?: string
  birthDate?: Date
  allergies: Allergy[]
  preferences: ClientPreferences
  tags: string[]
  vipStatus: boolean
  photos: ClientPhoto[]
  notes: string
  createdAt: Date
  lastVisit?: Date
}

export interface Allergy {
  id: string
  name: string
  severity: 'low' | 'medium' | 'high'
  notes?: string
}

export interface ClientPreferences {
  preferredBrands: string[]
  avoidIngredients: string[]
  maxProcessingTime: number
  priceRange: { min: number; max: number }
}

export interface ClientPhoto {
  id: string
  url: string
  type: 'before' | 'after' | 'reference'
  serviceId?: string
  createdAt: Date
}

// Hair Analysis Types
export interface HairAnalysis {
  id: string
  clientId: string
  performedBy: string
  zones: HairZone[]
  aiAnalysis: AIAnalysisResult
  confidenceScore: number
  images: AnalysisImage[]
  createdAt: Date
}

export interface HairZone {
  name: 'roots' | 'mids' | 'ends' | 'problem_areas'
  level: number // 1-10 scale
  tone: 'warm' | 'neutral' | 'cool'
  grayPercentage: number
  porosity: 'low' | 'medium' | 'high'
  condition: 'healthy' | 'damaged' | 'severely_damaged'
  previousChemicals: Chemical[]
}

export interface Chemical {
  type: 'color' | 'bleach' | 'perm' | 'relaxer'
  appliedDate: Date
  brand?: string
  notes?: string
}

export interface AIAnalysisResult {
  overallLevel: number
  dominantTone: string
  grayDistribution: string
  damageAssessment: string
  recommendations: string[]
  warnings: string[]
}

export interface AnalysisImage {
  id: string
  url: string
  zone: string
  quality: number
  metadata: ImageMetadata
}

export interface ImageMetadata {
  lighting: 'good' | 'fair' | 'poor'
  focus: 'sharp' | 'acceptable' | 'blurry'
  angle: 'optimal' | 'acceptable' | 'poor'
}

// Formula Types
export interface Formula {
  id: string
  analysisId: string
  createdBy: string
  zones: FormulaZone[]
  totalProductCost: number
  suggestedPrice: number
  actualPrice?: number
  notes?: string
  tags: string[]
  isCorrection: boolean
  successRating?: number
  createdAt: Date
}

export interface FormulaZone {
  zone: string
  products: FormulaProduct[]
  developer: Developer
  processingTime: number
  instructions: string[]
  cost: number
}

export interface FormulaProduct {
  id: string
  brand: string
  line: string
  shade: string
  amount: number // in grams/ml
  unitCost: number
}

export interface Developer {
  volume: number // 6, 9, 12, etc.
  amount: number
  cost: number
}

// Product and Inventory Types
export interface Product {
  id: string
  organizationId: string
  brand: string
  line: string
  name: string
  code: string
  type: 'color' | 'developer' | 'treatment' | 'other'
  currentStock: number
  unitCost: number
  reorderPoint: number
  properties: ProductProperties
  createdAt: Date
}

export interface ProductProperties {
  level?: number
  tone?: string
  volume?: number
  size: number
  unit: 'ml' | 'g' | 'oz'
  ingredients: string[]
  ppd: boolean
}

// Service Types
export interface Service {
  id: string
  clientId: string
  formulaId: string
  performedBy: string
  scheduledAt: Date
  startedAt?: Date
  completedAt?: Date
  processNotes: ProcessNote[]
  photos: ServicePhoto[]
  totalCharged: number
  paymentMethod?: string
  clientRating?: number
  clientFeedback?: string
  createdAt: Date
}

export interface ProcessNote {
  time: Date
  note: string
  type: 'voice' | 'text' | 'system'
  zone?: string
}

export interface ServicePhoto {
  id: string
  url: string
  type: 'before' | 'during' | 'after'
  zone?: string
  timestamp: Date
}

// Analytics Types
export interface BusinessMetrics {
  period: DateRange
  revenue: number
  services: number
  averageTicket: number
  profitMargin: number
  clientRetention: number
  topServices: ServiceMetric[]
  topProducts: ProductMetric[]
}

export interface DateRange {
  start: Date
  end: Date
}

export interface ServiceMetric {
  name: string
  count: number
  revenue: number
  margin: number
}

export interface ProductMetric {
  name: string
  usage: number
  cost: number
  efficiency: number
}

// UI and State Types
export interface AppState {
  user: User | null
  organization: Organization | null
  isLoading: boolean
  error: string | null
}

export interface Toast {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  description?: string
  duration?: number
}
