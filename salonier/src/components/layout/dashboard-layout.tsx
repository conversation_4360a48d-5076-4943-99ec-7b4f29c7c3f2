'use client'

import { useRequireAuth } from '@/lib/auth-context'
import { Sidebar } from './sidebar'
import { Header } from './header'
import { LoadingScreen } from '@/components/ui/loading'

interface DashboardLayoutProps {
  children: React.ReactNode
  title: string
  subtitle?: string
}

export function DashboardLayout({ children, title, subtitle }: DashboardLayoutProps) {
  const { user, isLoading } = useRequireAuth()

  if (isLoading) {
    return <LoadingScreen message="Verificando autenticación..." />
  }

  if (!user) {
    return null // useRequireAuth will redirect to login
  }

  return (
    <div className="min-h-screen bg-background">
      <Sidebar />
      
      <div className="lg:pl-64">
        <Header title={title} subtitle={subtitle} />
        
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  )
}
