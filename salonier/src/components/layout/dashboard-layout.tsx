'use client'

import { useRequireAuth } from '@/lib/auth-context'
import { Sidebar } from './sidebar'
import { Head<PERSON> } from './header'

interface DashboardLayoutProps {
  children: React.ReactNode
  title: string
  subtitle?: string
}

export function DashboardLayout({ children, title, subtitle }: DashboardLayoutProps) {
  const { user, isLoading } = useRequireAuth()

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-2xl flex items-center justify-center animate-pulse">
            <span className="text-2xl font-bold text-white">S</span>
          </div>
          <div>
            <h1 className="text-3xl font-bold gradient-text">Salonier</h1>
            <p className="text-muted-foreground mt-2">Cargando...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // useRequireAuth will redirect to login
  }

  return (
    <div className="min-h-screen bg-background">
      <Sidebar />
      
      <div className="lg:pl-64">
        <Header title={title} subtitle={subtitle} />
        
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  )
}
