import { 
  User, 
  Organization, 
  Client, 
  Product, 
  Service, 
  Formula,
  HairAnalysis,
  BusinessMetrics 
} from '@/types'

// Mock Organizations
export const mockOrganizations: Organization[] = [
  {
    id: 'org-1',
    name: '<PERSON><PERSON>',
    type: 'salon',
    subscriptionTier: 'pro',
    settings: {
      currency: 'USD',
      timezone: 'America/New_York',
      businessHours: {
        monday: { open: '09:00', close: '18:00', closed: false },
        tuesday: { open: '09:00', close: '18:00', closed: false },
        wednesday: { open: '09:00', close: '18:00', closed: false },
        thursday: { open: '09:00', close: '20:00', closed: false },
        friday: { open: '09:00', close: '20:00', closed: false },
        saturday: { open: '08:00', close: '17:00', closed: false },
        sunday: { open: '10:00', close: '16:00', closed: false },
      },
      defaultMargin: 65
    },
    createdAt: new Date('2024-01-15')
  }
]

// Mock Users
export const mockUsers: User[] = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'owner',
    organizationId: 'org-1',
    avatar: '/avatars/maria.jpg',
    preferences: {
      theme: 'light',
      language: 'es',
      notifications: true,
      defaultBrands: ['Wella', 'L\'Oréal', 'Matrix']
    },
    createdAt: new Date('2024-01-15')
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    name: 'Sofía Rodríguez',
    role: 'colorist',
    organizationId: 'org-1',
    avatar: '/avatars/sofia.jpg',
    preferences: {
      theme: 'light',
      language: 'es',
      notifications: true,
      defaultBrands: ['Wella', 'Schwarzkopf']
    },
    createdAt: new Date('2024-02-01')
  }
]

// Mock Clients
export const mockClients: Client[] = [
  {
    id: 'client-1',
    organizationId: 'org-1',
    name: 'Laura García',
    email: '<EMAIL>',
    phone: '******-0123',
    birthDate: new Date('1985-03-15'),
    allergies: [
      {
        id: 'allergy-1',
        name: 'PPD (Para-fenilendiamina)',
        severity: 'high',
        notes: 'Reacción severa en prueba anterior'
      }
    ],
    preferences: {
      preferredBrands: ['Wella', 'Matrix'],
      avoidIngredients: ['PPD', 'Ammonia'],
      maxProcessingTime: 60,
      priceRange: { min: 80, max: 200 }
    },
    tags: ['VIP', 'Sensible'],
    vipStatus: true,
    photos: [],
    notes: 'Cliente muy fiel, prefiere tonos fríos. Siempre puntual.',
    createdAt: new Date('2024-01-20'),
    lastVisit: new Date('2024-06-15')
  },
  {
    id: 'client-2',
    organizationId: 'org-1',
    name: 'Carmen Ruiz',
    email: '<EMAIL>',
    phone: '******-0124',
    birthDate: new Date('1978-08-22'),
    allergies: [],
    preferences: {
      preferredBrands: ['L\'Oréal'],
      avoidIngredients: [],
      maxProcessingTime: 90,
      priceRange: { min: 100, max: 300 }
    },
    tags: ['Regular'],
    vipStatus: false,
    photos: [],
    notes: 'Le gusta experimentar con colores nuevos.',
    createdAt: new Date('2024-02-10'),
    lastVisit: new Date('2024-06-10')
  },
  {
    id: 'client-3',
    organizationId: 'org-1',
    name: 'Ana Martínez',
    email: '<EMAIL>',
    phone: '******-0125',
    birthDate: new Date('1992-11-05'),
    allergies: [],
    preferences: {
      preferredBrands: ['Matrix', 'Schwarzkopf'],
      avoidIngredients: [],
      maxProcessingTime: 75,
      priceRange: { min: 60, max: 150 }
    },
    tags: ['Nueva'],
    vipStatus: false,
    photos: [],
    notes: 'Primera visita muy exitosa, quiere mantener color natural.',
    createdAt: new Date('2024-06-01'),
    lastVisit: new Date('2024-06-20')
  }
]

// Mock Products
export const mockProducts: Product[] = [
  {
    id: 'prod-1',
    organizationId: 'org-1',
    brand: 'Wella',
    line: 'Koleston Perfect',
    name: '6/0 Rubio Oscuro Natural',
    code: 'WK-60',
    type: 'color',
    currentStock: 12,
    unitCost: 8.50,
    reorderPoint: 3,
    properties: {
      level: 6,
      tone: 'natural',
      size: 60,
      unit: 'ml',
      ingredients: ['Etanolamina', 'Resorcinol'],
      ppd: false
    },
    createdAt: new Date('2024-01-15')
  },
  {
    id: 'prod-2',
    organizationId: 'org-1',
    brand: 'Wella',
    line: 'Welloxon Perfect',
    name: 'Revelador 20 Vol',
    code: 'WW-20',
    type: 'developer',
    currentStock: 8,
    unitCost: 12.00,
    reorderPoint: 2,
    properties: {
      volume: 20,
      size: 1000,
      unit: 'ml',
      ingredients: ['Peróxido de Hidrógeno'],
      ppd: false
    },
    createdAt: new Date('2024-01-15')
  },
  {
    id: 'prod-3',
    organizationId: 'org-1',
    brand: 'Matrix',
    line: 'SoColor',
    name: '7A Rubio Medio Ceniza',
    code: 'MS-7A',
    type: 'color',
    currentStock: 15,
    unitCost: 9.25,
    reorderPoint: 4,
    properties: {
      level: 7,
      tone: 'ash',
      size: 90,
      unit: 'ml',
      ingredients: ['MEA', 'Resorcinol'],
      ppd: false
    },
    createdAt: new Date('2024-01-20')
  }
]

// Mock Business Metrics
export const mockBusinessMetrics: BusinessMetrics = {
  period: {
    start: new Date('2024-06-01'),
    end: new Date('2024-06-30')
  },
  revenue: 12450,
  services: 89,
  averageTicket: 139.89,
  profitMargin: 68.5,
  clientRetention: 85.2,
  topServices: [
    { name: 'Balayage Completo', count: 23, revenue: 4140, margin: 72.1 },
    { name: 'Retoque de Raíz', count: 31, revenue: 2790, margin: 78.5 },
    { name: 'Corrección de Color', count: 12, revenue: 2880, margin: 65.3 },
    { name: 'Mechas Tradicionales', count: 18, revenue: 2340, margin: 69.8 },
    { name: 'Tinte Completo', count: 5, revenue: 300, margin: 75.0 }
  ],
  topProducts: [
    { name: 'Wella Koleston 6/0', usage: 450, cost: 38.25, efficiency: 92.3 },
    { name: 'Matrix SoColor 7A', usage: 380, cost: 35.15, efficiency: 89.7 },
    { name: 'Revelador 20 Vol', usage: 2100, cost: 25.20, efficiency: 95.1 },
    { name: 'Olaplex No.1', usage: 125, cost: 62.50, efficiency: 88.4 }
  ]
}

// Mock Recent Services
export const mockRecentServices: Service[] = [
  {
    id: 'service-1',
    clientId: 'client-1',
    formulaId: 'formula-1',
    performedBy: 'user-2',
    scheduledAt: new Date('2024-06-24T10:00:00'),
    startedAt: new Date('2024-06-24T10:15:00'),
    completedAt: new Date('2024-06-24T12:30:00'),
    processNotes: [
      {
        time: new Date('2024-06-24T10:15:00'),
        note: 'Iniciando aplicación en raíz',
        type: 'system',
        zone: 'roots'
      },
      {
        time: new Date('2024-06-24T10:45:00'),
        note: 'Cliente cómoda, sin irritación',
        type: 'voice'
      }
    ],
    photos: [],
    totalCharged: 145.00,
    paymentMethod: 'card',
    clientRating: 5,
    clientFeedback: '¡Perfecto como siempre! Me encanta el resultado.',
    createdAt: new Date('2024-06-24T10:00:00')
  }
]

// Helper functions for mock data
export function getClientById(id: string): Client | undefined {
  return mockClients.find(client => client.id === id)
}

export function getUserById(id: string): User | undefined {
  return mockUsers.find(user => user.id === id)
}

export function getProductById(id: string): Product | undefined {
  return mockProducts.find(product => product.id === id)
}

export function getClientsByOrganization(orgId: string): Client[] {
  return mockClients.filter(client => client.organizationId === orgId)
}

export function getProductsByOrganization(orgId: string): Product[] {
  return mockProducts.filter(product => product.organizationId === orgId)
}
