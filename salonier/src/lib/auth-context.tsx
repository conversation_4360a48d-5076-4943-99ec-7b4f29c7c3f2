'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { User, Organization } from '@/types'
import { mockUsers, mockOrganizations } from '@/data/mockData'
import { LoadingScreen } from '@/components/ui/loading'

interface AuthContextType {
  user: User | null
  organization: Organization | null
  isLoading: boolean
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simular carga inicial - verificar si hay usuario guardado
    const savedUser = localStorage.getItem('salonier-user')
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser)
        const foundUser = mockUsers.find(u => u.id === userData.id)
        if (foundUser) {
          setUser(foundUser)
          const userOrg = mockOrganizations.find(org => org.id === foundUser.organizationId)
          setOrganization(userOrg || null)
        }
      } catch (error) {
        console.error('Error loading saved user:', error)
        localStorage.removeItem('salonier-user')
      }
    }
    setIsLoading(false)
  }, [])

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true)
    
    // Simular delay de autenticación
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Buscar usuario en datos mock
    const foundUser = mockUsers.find(u => u.email === email)
    
    if (foundUser && password === 'demo123') { // Password mock para demo
      setUser(foundUser)
      const userOrg = mockOrganizations.find(org => org.id === foundUser.organizationId)
      setOrganization(userOrg || null)
      
      // Guardar en localStorage
      localStorage.setItem('salonier-user', JSON.stringify({ id: foundUser.id }))
      
      setIsLoading(false)
      return true
    }
    
    setIsLoading(false)
    return false
  }

  const logout = () => {
    setUser(null)
    setOrganization(null)
    localStorage.removeItem('salonier-user')
  }

  const value: AuthContextType = {
    user,
    organization,
    isLoading,
    login,
    logout,
    isAuthenticated: !!user
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Hook para proteger rutas
export function useRequireAuth() {
  const { user, isLoading } = useAuth()
  
  useEffect(() => {
    if (!isLoading && !user) {
      window.location.href = '/auth/login'
    }
  }, [user, isLoading])
  
  return { user, isLoading }
}
